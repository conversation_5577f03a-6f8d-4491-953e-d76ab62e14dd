document.addEventListener("DOMContentLoaded", function() {
    const loadComponent = (url, placeholderId) => {
        const placeholder = document.getElementById(placeholderId);
        if (!placeholder) {
            console.warn(`Placeholder element with ID '${placeholderId}' not found.`);
            return Promise.resolve(); // Resolve immediately if placeholder missing, so Promise.all doesn't fail
        }
        return fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Failed to load ${url}: ${response.status} ${response.statusText}`);
                }
                return response.text();
            })
            .then(data => {
                // Replace the placeholder div itself with the fetched content
                // This is better than innerHTML if the fetched content is a single root element (e.g. <header> or <footer>)
                placeholder.outerHTML = data;
            })
            .catch(error => {
                console.error(error);
                if (placeholder) placeholder.innerHTML = `<p style="color:red; text-align:center;">Error loading content from ${url}.</p>`;
            });
    };

    const headerPromise = loadComponent('includes/header.html', 'header-placeholder');
    const footerPromise = loadComponent('includes/footer.html', 'footer-placeholder');

    Promise.all([headerPromise, footerPromise])
        .then(() => {
            console.log("Header and Footer HTML successfully loaded and injected.");
            
            // Now that the header and footer HTML is in place, dynamically load main.js
            // This ensures main.js executes after the DOM elements it might target are available.
            const script = document.createElement('script');
            script.src = 'assets/js/main.js'; // Path to your main template script
            script.onload = () => {
                console.log('assets/js/main.js loaded.');

                // Explicitly initialize components after main.js is loaded and DOM is updated

                // Hero Slider (Swiper)
                if (typeof Swiper !== 'undefined' && document.querySelector(".sliderFeaturedPosts")) {
                    try {
                        console.log("Initializing Swiper for .sliderFeaturedPosts from include.js");
                        new Swiper(".sliderFeaturedPosts", {
                            spaceBetween: 0,
                            speed: 500,
                            centeredSlides: true,
                            loop: true,
                            slideToClickedSlide: true,
                            autoplay: {
                                delay: 3000,
                                disableOnInteraction: false,
                            },
                            pagination: {
                                el: ".swiper-pagination",
                                clickable: true,
                            },
                            navigation: {
                                nextEl: ".custom-swiper-button-next",
                                prevEl: ".custom-swiper-button-prev",
                            },
                        });
                        console.log("Swiper initialization from include.js completed.");
                    } catch (e) {
                        console.error("Error initializing Swiper from include.js:", e);
                    }
                }

                // GLightbox
                if (typeof GLightbox !== 'undefined') {
                    try {
                        console.log("Initializing GLightbox from include.js");
                        GLightbox({
                            selector: '.glightbox'
                        });
                        console.log("GLightbox initialization from include.js completed.");
                    } catch (e) {
                        console.error("Error initializing GLightbox from include.js:", e);
                    }
                }

                // AOS (Animation on Scroll)
                if (typeof AOS !== 'undefined') {
                    try {
                        console.log("Initializing AOS from include.js");
                        AOS.init({
                            duration: 1000,
                            easing: 'ease-in-out',
                            once: true,
                            mirror: false
                        });
                        // AOS.refresh(); // Might be needed if content was previously hidden/altered
                        console.log("AOS initialization from include.js completed.");
                    } catch (e) {
                        console.error("Error initializing AOS from include.js:", e);
                    }
                }
                
                // jQuery submenu click handlers (from original main.js)
                if (typeof $ !== 'undefined') {
                    try {
                        console.log("Attaching jQuery submenu click handlers from include.js");
                        $('.dropdown-submenu .dropdown-toggle').off('click').on("click", function (e) {
                            e.preventDefault();
                            e.stopPropagation();
                            $(this).next('.dropdown-menu').toggleClass('show');
                        });
                        console.log("jQuery submenu click handlers attached from include.js.");
                    } catch (e) {
                        console.error("Error attaching jQuery submenu click handlers from include.js:", e);
                    }
                }
                
                // Call any other functions from main.js that need to run after full setup
                // For example, if main.js had a global function like: function globalPageInit() { ... }
                // You could call it here: if(typeof globalPageInit === 'function') globalPageInit();

                console.log('Component initializations from include.js attempted.');
            };
            script.onerror = () => {
                console.error('Error loading assets/js/main.js');
            };
            document.body.appendChild(script); // Append to body to execute
        })
        .catch(error => {
            // This catch is for Promise.all, if any of the loadComponent promises are rejected
            // (though loadComponent itself tries to handle errors gracefully for its placeholder)
            console.error("Error loading one or more components (header/footer):", error);
        });
}); 