document.addEventListener('DOMContentLoaded', async function() {
    // Load header
    const headerResponse = await fetch('includes/header.html');
    const headerContent = await headerResponse.text();
    document.getElementById('header-placeholder').innerHTML = headerContent;

    // Load footer
    const footerResponse = await fetch('includes/footer.html');
    const footerContent = await footerResponse.text();
    document.getElementById('footer-placeholder').innerHTML = footerContent;

    // Load main.js after components are loaded to ensure proper initialization
    const mainScript = document.createElement('script');
    mainScript.src = 'assets/js/main.js';
    mainScript.onload = function() {
        // Explicitly initialize components after main.js is loaded
        if (typeof GLightbox !== 'undefined') {
            GLightbox({
                selector: '.glightbox'
            });
        }

        // Initialize Swiper if it exists and there's a slider
        if (typeof Swiper !== 'undefined' && document.querySelector('.sliderFeaturedPosts')) {
            new Swiper(".sliderFeaturedPosts", {
                spaceBetween: 0,
                speed: 500,
                centeredSlides: true,
                loop: true,
                slideToClickedSlide: true,
                autoplay: {
                    delay: 3000,
                    disableOnInteraction: false,
                },
                pagination: {
                    el: ".swiper-pagination",
                    clickable: true,
                },
                navigation: {
                    nextEl: ".custom-swiper-button-next",
                    prevEl: ".custom-swiper-button-prev",
                },
            });
        }

        // Initialize AOS if it exists
        if (typeof AOS !== 'undefined') {
            AOS.init({
                duration: 1000,
                easing: 'ease-in-out',
                once: true,
                mirror: false
            });
        }
    };
    document.body.appendChild(mainScript);
}); 