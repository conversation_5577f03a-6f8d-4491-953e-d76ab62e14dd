﻿<?xml version="1.0" encoding="utf-8" ?>
<Configuration>
  <CommonConfiguration>
    <hibernate-configuration xmlns="urn:nhibernate-configuration-2.2">
      <session-factory>
        <property name="connection.provider">NHibernate.Connection.DriverConnectionProvider</property>
        <property name="show_sql">false</property>
        <property name="command_timeout">600</property>
        <property name="query.substitutions">true 1, false 0, yes 'Y', no 'N'</property>
      </session-factory>
    </hibernate-configuration>
  </CommonConfiguration>
  <Databases>
    <!--DBFlavour is the enum type in connctionUtility2-->
    <Database type="PostgreSQL" dbflavour="PostgreSQL" defaultport="5432">
      <hibernate-configuration xmlns="urn:nhibernate-configuration-2.2">
        <session-factory>
          <property name="dialect">DHI.Solutions.Generic.Data.DSSPostgreSQLDialect, DHI.Solutions.Generic.Data</property>
          <property name="connection.driver_class">NHibernate.Driver.NpgsqlDriver</property>
        </session-factory>
      </hibernate-configuration>
    </Database>
    <Database type="Oracle" dbflavour="Oracle" defaultport="1521">
      <hibernate-configuration xmlns="urn:nhibernate-configuration-2.2">
        <session-factory>
          <property name="dialect">NHibernate.Dialect.Oracle10gDialect</property>
          <property name="connection.driver_class">NHibernate.Driver.OracleDataClientDriver</property>
        </session-factory>
      </hibernate-configuration>
    </Database>
    <Database type="SQLite" dbflavour="SQLite">
      <hibernate-configuration xmlns="urn:nhibernate-configuration-2.2">
        <session-factory>
          <property name="dialect">NHibernate.Dialect.SQLiteDialect</property>
          <property name="connection.driver_class">NHibernate.Driver.SQLite20Driver</property>
          <!--<property name="query.substitutions">true=1;false=0</property>-->
        </session-factory>
      </hibernate-configuration>
    </Database>
    <Database type="MSSQL" dbflavour="MSSQL" defaultport="1433">
      <hibernate-configuration xmlns="urn:nhibernate-configuration-2.2">
        <session-factory>
          <property name="adonet.batch_size">0</property>
          <property name="dialect">NHibernate.Dialect.MsSql2012Dialect</property>
          <property name="connection.driver_class">NHibernate.Driver.SqlClientDriver</property>
		  <property name="hbm2ddl.keywords">auto-quote</property>
          <!--<property name="query.substitutions">true=1;false=0</property>-->
        </session-factory>
      </hibernate-configuration>
    </Database>
    <Supported-database-versions>
      <version dbflavour="PostgreSQL" dbversion="10" postgis="16012" postgisversion="2.4.1-1"></version>
      <version dbflavour="PostgreSQL" dbversion="10" postgis="16312" postgisversion="2.4.3-1"></version>
      <version dbflavour="PostgreSQL" dbversion="10" postgis="16526" postgisversion="2.4.4-1"></version>
      <version dbflavour="PostgreSQL" dbversion="10" postgis="16836" postgisversion="2.5.0-1"></version>
      <version dbflavour="PostgreSQL" dbversion="10" postgis="17027" postgisversion="2.5.1-1"></version>
      <version dbflavour="PostgreSQL" dbversion="10" postgis="17328" postgisversion="2.5.2-1"></version>
      <version dbflavour="PostgreSQL" dbversion="10" postgis="17699" postgisversion="2.5.3-2"></version>
      <version dbflavour="PostgreSQL" dbversion="11" postgis="16836" postgisversion="2.5.0-1"></version>
      <version dbflavour="PostgreSQL" dbversion="11" postgis="17027" postgisversion="2.5.1-1"></version>
      <version dbflavour="PostgreSQL" dbversion="11" postgis="17328" postgisversion="2.5.2-1"></version>
      <version dbflavour="PostgreSQL" dbversion="11" postgis="17699" postgisversion="2.5.3-1"></version>
      <version dbflavour="PostgreSQL" dbversion="12" postgis="17983" postgisversion="3.0.0-4"></version>
      <version dbflavour="PostgreSQL" dbversion="12" postgis="3.0" postgisversion="3.0"></version>
      <version dbflavour="PostgreSQL" dbversion="13" postgis="3.1" postgisversion="3.1"></version>
      <version dbflavour="PostgreSQL" dbversion="13" postgis="3.2" postgisversion="3.2"></version>
      <version dbflavour="PostgreSQL" dbversion="14" postgis="3.1" postgisversion="3.1"></version>
      <version dbflavour="PostgreSQL" dbversion="14" postgis="3.2" postgisversion="3.2"></version>
    </Supported-database-versions>
  </Databases>
</Configuration>
