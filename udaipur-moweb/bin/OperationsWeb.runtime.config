<Products>
  <Product Name="DHI.Solutions.Core">
    <Plugins>
      <Plugin Name="DHI.Solutions.Generic.Business.Workspace" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.Generic.Business.dll" />
      <Plugin Name="DHI.Solutions.Generic.Business.DssAdmin" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.Generic.Business.dll" />
      <Plugin Name="DHI.Solutions.Generic.Business.UserWorkspace" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.Generic.Business.dll" />
      <Plugin Name="DHI.Solutions.Generic.Business.User" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.Generic.Business.dll" />
      <Plugin Name="DHI.Solutions.Generic.Business.GlobalRole" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.Generic.Business.dll" />
      <Plugin Name="DHI.Solutions.Generic.Business.WorkspaceRole" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.Generic.Business.dll" />
      <Plugin Name="DHI.Solutions.Generic.Business.UserLoginStatus" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.Generic.Business.dll" />
      <Plugin Name="DHI.Solutions.Generic.Data.FakeDTO" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.Generic.Data.dll" />
    </Plugins>
  </Product>
  <Product Name="DHI.Solutions.DocumentManager">
    <Plugins>
      <Plugin Name="DHI.Solutions.DocumentManager.Business.Module" Type="DHI.Solutions.Generic.IModule" Assembly="DHI.Solutions.DocumentManager.Business.dll" />
      <Plugin Name="DHI.Solutions.DocumentManager.Business.Document" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.DocumentManager.Business.dll" />
      <Plugin Name="DHI.Solutions.DocumentManager.Business.DocumentFeatureAssoc" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.DocumentManager.Business.dll" />
      <Plugin Name="DHI.Solutions.DocumentManager.Business.DocumentFolder" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.DocumentManager.Business.dll" />
      <Plugin Name="DHI.Solutions.DocumentManager.Business.DocumentFolderAssociation" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.DocumentManager.Business.dll" />
      <Plugin Name="DHI.Solutions.DocumentManager.Business.DocumentRelationship" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.DocumentManager.Business.dll" />
      <Plugin Name="DHI.Solutions.DocumentManager.Data.FakeDTO" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.DocumentManager.Data.dll" />
	  <Plugin Name="DHI.Solutions.DocumentManager.DocumentDataProvider.DocumentDataProvider" Type="DHI.Solutions.DocumentManager.Interfaces.IDocumentDataProvider" Assembly="DHI.Solutions.DocumentManager.DocumentDataProvider.dll" />
    </Plugins>
  </Product>
  <Product Name="DHI.Solutions.GISManager">
    <Plugins>
      <Plugin Name="DHI.Solutions.GISManager.Business.Module" Type="DHI.Solutions.Generic.IModule" Assembly="DHI.Solutions.GISManager.Business.dll" />
      <Plugin Name="DHI.Solutions.GISManager.PostGISDataProvider.PostGISDataProvider" Type="DHI.Solutions.GISManager.Interfaces.IGISDataProvider" Assembly="DHI.Solutions.GISManager.PostGISDataProvider.dll" />
      <Plugin Name="DHI.Solutions.GISManager.PostGISRasterDataProvider.PostGISRasterDataProvider" Type="DHI.Solutions.GISManager.Interfaces.IRasterDataProvider" Assembly="DHI.Solutions.GISManager.PostGISRasterDataProvider.dll" />
      <Plugin Name="DHI.Solutions.GISManager.RemoteFileBasedRasterProvider.RemoteFileBasedRasterProvider" Type="DHI.Solutions.GISManager.Interfaces.IRasterDataProvider" Assembly="DHI.Solutions.GISManager.RemoteFileBasedRasterProvider.dll" InitializeData="WorkspaceSettings" />
      <Plugin Name="DHI.Solutions.GISManager.GISProcessor.GISProcessor" Type="DHI.Solutions.GISManager.Interfaces.IGISProcessor" Assembly="DHI.Solutions.GISManager.GISProcessor.dll" />
      <Plugin Name="DHI.Solutions.GISManager.PostGISRasterProcessor.PostGISRasterProcessor" Type="DHI.Solutions.GISManager.Interfaces.IRasterProcessor" Assembly="DHI.Solutions.GISManager.PostGISRasterProcessor.dll" />
      <Plugin Name="DHI.Solutions.GISManager.GISRasterProcessor.GISRasterProcessor" Type="DHI.Solutions.GISManager.Interfaces.IRasterProcessor" Assembly="DHI.Solutions.GISManager.GISRasterProcessor.dll" />
      <Plugin Name="DHI.Solutions.GISManager.UI.Tools.Dfs2ImportTool.Dfs2ImportTool" Type="DHI.Solutions.Generic.ITool" Assembly="DHI.Solutions.GISManager.UI.Tools.Dfs2ImportTool.dll" />
      <Plugin Name="DHI.Solutions.GISManager.UI.Tools.NetCDFImportTool.NetCDFImportTool" Type="DHI.Solutions.Generic.ITool" Assembly="DHI.Solutions.GISManager.UI.Tools.NetCDFImportTool.dll" />
      <Plugin Name="DHI.Solutions.GISManager.Data.FakeDTO" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.GISManager.Data.dll" />
    </Plugins>
  </Product>
  <Product Name="DHI.Solutions.JobManager">
    <Plugins>
      <Plugin Name="DHI.Solutions.JobManager.Data.FakeDTO" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.JobManager.Data.dll" />
      <Plugin Name="DHI.Solutions.JobManager.Business.Module" Type="DHI.Solutions.Generic.IModule" Assembly="DHI.Solutions.JobManager.Business.dll" />
    </Plugins>
  </Product>
  <Product Name="DHI.Solutions.MetadataManager">
    <Plugins>
      <Plugin Name="DHI.Solutions.MetadataManager.Tools.ChangeLogQueryTool.ChangeLogQueryTool" Type="DHI.Solutions.Generic.ITool" Assembly="DHI.Solutions.MetadataManager.Tools.ChangeLogQueryTool.dll" />
      <Plugin Name="DHI.Solutions.MetadataManager.Data.FakeDTO" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.MetadataManager.Data.dll" />
      <Plugin Name="DHI.Solutions.MetadataManager.Business.ChangeLogModule" Type="DHI.Solutions.Generic.IModule" Assembly="DHI.Solutions.MetadataManager.Business.dll" />
      <Plugin Name="DHI.Solutions.MetadataManager.Business.MetadataModule" Type="DHI.Solutions.Generic.IModule" Assembly="DHI.Solutions.MetadataManager.Business.dll" />
      <Plugin Name="DHI.Solutions.MetadataManager.Business.ChangeLogEntry" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.MetadataManager.Business.dll" />
      <Plugin Name="DHI.Solutions.MetadataManager.Business.Metadata" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.MetadataManager.Business.dll" />
      <Plugin Name="DHI.Solutions.MetadataManager.Business.MetadataSchema" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.MetadataManager.Business.dll" />
      <Plugin Name="DHI.Solutions.MetadataManager.Business.Language" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.MetadataManager.Business.dll" />
      <Plugin Name="DHI.Solutions.MetadataManager.Business.TranslatedKey" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.MetadataManager.Business.dll" />
    </Plugins>
  </Product>
  <Product Name="DHI.Solutions.ScenarioManager">
    <Plugins>
      <Plugin Name="DHI.Solutions.ScenarioManager.Business.Module" Type="DHI.Solutions.Generic.IModule" Assembly="DHI.Solutions.ScenarioManager.Business.dll" />
      <Plugin Name="DHI.Solutions.ScenarioManager.Business.ModelSetupIndicatorDefinition" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.ScenarioManager.Business.dll" />
      <Plugin Name="DHI.Solutions.ScenarioManager.Business.SimulationIndicator" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.ScenarioManager.Business.dll" />
      <Plugin Name="DHI.Solutions.ScenarioManager.Data.FakeDTO" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.ScenarioManager.Data.dll" />
      <Plugin Name="DHI.Solutions.Adapters.MIKE11.MIKE11Adapter" Type="DHI.Solutions.ScenarioManager.Interfaces.IAdapter" Assembly="DHI.Solutions.Adapters.MIKE11.dll" />
      <Plugin Name="DHI.Solutions.Adapters.MIKE1D.MIKE1DAdapter" Type="DHI.Solutions.ScenarioManager.Interfaces.IAdapter" Assembly="DHI.Solutions.Adapters.MIKE1D.dll" />
      <Plugin Name="DHI.Solutions.Adapters.MIKEHydroRiver.MIKEHydroRiverAdapter" Type="DHI.Solutions.ScenarioManager.Interfaces.IAdapter" Assembly="DHI.Solutions.Adapters.MIKEHydroRiver.dll" />
      <Plugin Name="DHI.Solutions.ScenarioManager.Tools.RStatisticsTool.RStatisticsSkillScoresTool" Type="DHI.Solutions.Generic.ITool" Assembly="DHI.Solutions.ScenarioManager.Tools.RStatisticsTool.dll" />
      <Plugin Name="DHI.Solutions.ScenarioManager.Tools.RStatisticsTool.RStatisticsTool" Type="DHI.Solutions.Generic.ITool" Assembly="DHI.Solutions.ScenarioManager.Tools.RStatisticsTool.dll" />
      <Plugin Name="DHI.Solutions.ScenarioManager.Tools.RStatisticsTool.RStatisticsConfidenceIntervalTool" Type="DHI.Solutions.Generic.ITool" Assembly="DHI.Solutions.ScenarioManager.Tools.RStatisticsTool.dll" />
      <Plugin Name="DHI.Solutions.ScenarioManager.Tools.RStatisticsTool.RStatisticsGoodnessOfFitTool" Type="DHI.Solutions.Generic.ITool" Assembly="DHI.Solutions.ScenarioManager.Tools.RStatisticsTool.dll" />
      <Plugin Name="DHI.Solutions.ScenarioManager.Tools.SimulationTimeSeriesProfileTool.SimulationTimeSeriesProfileTool" Type="DHI.Solutions.Generic.ITool" Assembly="DHI.Solutions.ScenarioManager.Tools.SimulationTimeSeriesProfileTool.dll" />
    </Plugins>
  </Product>
  <Product Name="DHI.Solutions.ScriptManager">
    <Plugins>
      <Plugin Name="DHI.Solutions.ScriptManager.Data.Foo" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.ScriptManager.Data.dll" />
      <Plugin Name="DHI.Solutions.ScriptManager.Business.ScriptStorage" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.ScriptManager.Business.dll" />
      <Plugin Name="DHI.Solutions.ScriptManager.Business.Module" Type="DHI.Solutions.Generic.IModule" Assembly="DHI.Solutions.ScriptManager.Business.dll" />
      <Plugin Name="DHI.Solutions.ScriptManager.Business.Script" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.ScriptManager.Business.dll" />
      <Plugin Name="DHI.Solutions.ScriptManager.Business.ScriptGroup" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.ScriptManager.Business.dll" />
      <Plugin Name="DHI.Solutions.ScriptManager.Business.PreparedScript" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.ScriptManager.Business.dll" />
    </Plugins>
  </Product>
  <Product Name="DHI.Solutions.SpreadsheetManager">
    <Plugins>
      <Plugin Name="DHI.Solutions.SpreadsheetManager.Business.Module" Type="DHI.Solutions.Generic.IModule" Assembly="DHI.Solutions.SpreadsheetManager.Business.dll" />
      <Plugin Name="DHI.Solutions.SpreadsheetManager.Business.Spreadsheet" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.SpreadsheetManager.Business.dll" />
      <Plugin Name="DHI.Solutions.SpreadsheetManager.Business.SpreadsheetFeatureAssociation" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.SpreadsheetManager.Business.dll" />
      <Plugin Name="DHI.Solutions.SpreadsheetManager.Business.SpreadsheetGroup" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.SpreadsheetManager.Business.dll" />
      <Plugin Name="DHI.Solutions.SpreadsheetManager.Data.FakeDTO" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.SpreadsheetManager.Data.dll" />
      <Plugin Name="DHI.Solutions.SpreadsheetManager.Tools.QueryTool.QueryTool" Type="DHI.Solutions.Generic.ITool" Assembly="DHI.Solutions.SpreadsheetManager.Tools.QueryTool.dll" />
    </Plugins>
  </Product>
  <Product Name="DHI.Solutions.SystemManager">
    <Plugins>
      <Plugin Name="DHI.Solutions.SystemManager.Business.Module" Type="DHI.Solutions.Generic.IModule" Assembly="DHI.Solutions.SystemManager.Business.dll" />
      <Plugin Name="DHI.Solutions.SystemManager.Data.FakeDTO" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.SystemManager.Data.dll" />
    </Plugins>
  </Product>
  <Product Name="DHI.Solutions.TimeSeriesManager">
    <Plugins>
      <Plugin Name="DHI.Solutions.TimeseriesManager.Tools.QueryTool.QueryTool" Type="DHI.Solutions.Generic.ITool" Assembly="DHI.Solutions.TimeseriesManager.Tools.QueryTool.dll" />
      <Plugin Name="DHI.Solutions.TimeseriesManager.Tools.AdvancedStatistics.EnsembleStatisticsTool" Type="DHI.Solutions.Generic.ITool" Assembly="DHI.Solutions.TimeseriesManager.Tools.AdvancedStatistics.dll" />
      <Plugin Name="DHI.Solutions.TimeseriesManager.Tools.Processing.ExtractPeriodTool" Type="DHI.Solutions.Generic.ITool" Assembly="DHI.Solutions.TimeseriesManager.Tools.Processing.dll" />
      <Plugin Name="DHI.Solutions.TimeseriesManager.Tools.Processing.ExtractEnsembleMembers" Type="DHI.Solutions.Generic.ITool" Assembly="DHI.Solutions.TimeseriesManager.Tools.Processing.dll" />
      <Plugin Name="DHI.Solutions.TimeseriesManager.Tools.Processing.FilteringTool" Type="DHI.Solutions.Generic.ITool" Assembly="DHI.Solutions.TimeseriesManager.Tools.Processing.dll" />
      <Plugin Name="DHI.Solutions.TimeseriesManager.Tools.Processing.ResampleTool" Type="DHI.Solutions.Generic.ITool" Assembly="DHI.Solutions.TimeseriesManager.Tools.Processing.dll" />
      <Plugin Name="DHI.Solutions.TimeseriesManager.Business.Module" Type="DHI.Solutions.Generic.IModule" Assembly="DHI.Solutions.TimeseriesManager.Business.dll" />
      <Plugin Name="DHI.Solutions.TimeseriesManager.Business.TimeSeriesGroup" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.TimeseriesManager.Business.dll" />
      <Plugin Name="DHI.Solutions.TimeseriesManager.Business.TimeSeriesFeatureAssociation" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.TimeseriesManager.Business.dll" />
      <Plugin Name="DHI.Solutions.TimeseriesManager.Business.DataSeries" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.TimeseriesManager.Business.dll" />
      <Plugin Name="DHI.Solutions.TimeseriesManager.Business.FlagDefinition" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.TimeseriesManager.Business.dll" />
      <Plugin Name="DHI.Solutions.TimeseriesManager.Data.DataSeriesBlobDTO" Type="DHI.Solutions.Generic.IDTO" Assembly="DHI.Solutions.TimeseriesManager.Data.dll" />
      <Plugin Name="DHI.Solutions.TimeseriesManager.TimeSeriesDataProvider.TimeSeriesDataProvider" Type="DHI.Solutions.TimeseriesManager.Interfaces.ITimeSeriesDataProvider" Assembly="DHI.Solutions.TimeseriesManager.TimeSeriesDataProvider.dll" />
      <Plugin Name="DHI.Solutions.TimeseriesManager.TimeSeriesDIMSProvider.TimeSeriesDIMSProvider" Type="DHI.Solutions.TimeseriesManager.Interfaces.ITimeSeriesDataProvider" Assembly="DHI.Solutions.TimeseriesManager.TimeSeriesDIMSProvider.dll" InitializeData="WorkspaceSettings" />
      <Plugin Name="DHI.Solutions.TimeseriesManager.TimeSeriesDIMSProvider.RegisterDIMSTimeSeriesDataView" Type="DHI.Solutions.Generic.IDataViewControl" Assembly="DHI.Solutions.TimeseriesManager.TimeSeriesDIMSProvider.dll" />
      <Plugin Name="DHI.Solutions.TimeseriesManager.TimeSeriesOpenMIProvider.TimeSeriesOpenMIProvider" Type="DHI.Solutions.TimeseriesManager.Interfaces.ITimeSeriesDataProvider" Assembly="DHI.Solutions.TimeseriesManager.TimeSeriesOpenMIProvider.dll" InitializeData="WorkspaceSettings" />
      <Plugin Name="DHI.Solutions.TimeseriesManager.TimeSeriesOpenMIProvider.RegisterOpenMITimeSeriesDataView" Type="DHI.Solutions.Generic.IDataViewControl" Assembly="DHI.Solutions.TimeseriesManager.TimeSeriesOpenMIProvider.dll" />
    </Plugins>
  </Product>
  <Product Name="DHI.Solutions.RealtimeManager">
    <Plugins>
      <Plugin
        Name="DHI.Solutions.RealtimeManager.Business.RealtimeModule"
        Type="DHI.Solutions.Generic.IModule"
        Assembly="DHI.Solutions.RealtimeManager.Business.dll" />
      <Plugin
        Name="DHI.Solutions.RealtimeManager.Business.ThresholdCache"
        Type="DHI.Solutions.Generic.IDTO"
        Assembly="DHI.Solutions.RealtimeManager.Business.dll" />
	  <Plugin
        Name="DHI.Solutions.RealtimeManager.Data.FakeDTO"
        Type="DHI.Solutions.Generic.IDTO"
        Assembly="DHI.Solutions.RealtimeManager.Data.dll" />
    </Plugins>
  </Product>
  <Product Name="License">
    <Plugins>
      <Plugin Name="DHI.Solutions.LicenseProvider.LicenseProvider" Type="DHI.Solutions.Generic.ILicenseProvider" Assembly="DHI.Solutions.LicenseProvider.dll" />
    </Plugins>
  </Product>
  <Product Name="CustomPlugins">
    <Plugins />
  </Product>
</Products>
