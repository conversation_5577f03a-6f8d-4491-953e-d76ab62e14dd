﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <section name="DHI.Solutions.Shell.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    </sectionGroup>
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler,log4net" />
    <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <section name="DHI.Solutions.Shell.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
    </sectionGroup>
  </configSections>

  <!-- Logging -->
  <!--
  <system.diagnostics>
    <sources>
      <source name="DSSDebugTrace" switchValue="Error">
        <listeners>
          <add initializeData="c:\temp\trace.svclog" type="System.Diagnostics.XmlWriterTraceListener" name="DebugListener"/>
        </listeners>
      </source>
      <source name="DSSPerformanceTrace" switchValue="Off">
        <listeners>
          <add initializeData="c:\temp\performance.svclog" type="System.Diagnostics.XmlWriterTraceListener" name="PerfListener"/>
        </listeners>
      </source>
    </sources>
  </system.diagnostics>
  -->


  <!-- Allow loading of mixed-mode assemblies built against .Net 2.0 -->
  <startup useLegacyV2RuntimeActivationPolicy="true">
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2" />
  </startup>
  
  <!-- This section contains the log4net configuration settings -->
  <log4net>
    <!-- Define some output appenders -->
    <appender name="trace" type="log4net.Appender.TraceAppender, log4net">
      <layout type="log4net.Layout.PatternLayout,log4net">
        <param name="ConversionPattern" value="%d [%t] %-5p %c - %m%n" />
      </layout>
    </appender>
    <appender name="console" type="log4net.Appender.ConsoleAppender, log4net">
      <layout type="log4net.Layout.PatternLayout,log4net">
        <param name="ConversionPattern" value="%d [%t] %-5p %c  - %m%n" />
      </layout>
    </appender>
    <root>
      <level value="INFO" />
      <appender-ref ref="console" />
    </root>
    <appender name="MyNhLog" type="log4net.Appender.RollingFileAppender,log4net">
      <param name="File" value="c:\temp\NHibernate.log" />
      <param name="AppendToFile" value="false" />
      <layout type="log4net.Layout.PatternLayout">
        <param name="ConversionPattern" value="%d [%t] %-5p %c - %m%n" />
      </layout>
    </appender>
    <logger name="NHibernate.SQL">
      <!-- ALL,DEBUG, INFO, WARN, ERROR, FATAL/OFF -->
      <level value="ALL" />
      <appender-ref ref="MyNhLog" />
    </logger>
    <logger name="NHibernate" additivity="false">
      <level value="OFF" />
      <appender-ref ref="MyNhLog" />
    </logger>
  </log4net>
  <system.windows.forms jitDebugging="true" />

  
  <applicationSettings>
    <DHI.Solutions.Shell.Properties.Settings>
      <setting name="ApplicationName" serializeAs="String">
        <value>DHI Solutions</value>
      </setting>
      <setting name="StartPageURL" serializeAs="String">
        <value>http://smaweb.extranet.dhigroup.com/SitePages/ClientInfo.aspx?dongle=id&amp;app=mo</value>
      </setting>
      <setting name="IconURL" serializeAs="String">
        <value />
      </setting>
      <setting name="ForceCulture" serializeAs="String">
        <value />
      </setting>
      <setting name="UserInitiatedSave" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="RunMode" serializeAs="String">
        <value>1</value>
      </setting>
      <setting name="SplashScreenTitle" serializeAs="String">
        <value>Solution software</value>
      </setting>
      <setting name="SplashScreenSubTitle" serializeAs="String">
        <value>by DHI </value>
      </setting>
      <setting name="SplashScreenTitleColor" serializeAs="String">
        <value>LightSteelBlue</value>
      </setting>
      <setting name="SplashScreenTitleFont" serializeAs="String">
        <value>Microsoft Sans Serif, 18pt, style=Bold</value>
      </setting>
      <setting name="SplashScreenSubTitleColor" serializeAs="String">
        <value>LightSteelBlue</value>
      </setting>
      <setting name="SplashScreenSubTitleFont" serializeAs="String">
        <value>Microsoft Sans Serif, 8.25pt, style=Bold</value>
      </setting>
      <setting name="SplashScreenVersion" serializeAs="String">
        <value>Release 1.8</value>
      </setting>
      <setting name="SplashBackgroundImagePath" serializeAs="String">
        <value />
      </setting>
      <setting name="SplashScreenLoadingTextColor" serializeAs="String">
        <value>Gray</value>
      </setting>
      <setting name="SplashScreenLoadingTextFont" serializeAs="String">
        <value>Microsoft Sans Serif, 8.25pt</value>
      </setting>
      <setting name="SplashScreenVersionColor" serializeAs="String">
        <value>LightSteelBlue</value>
      </setting>
      <setting name="SplashScreenVersionFont" serializeAs="String">
        <value>Microsoft Sans Serif, 8.25pt</value>
      </setting>
      <setting name="SplashBackgroundColor" serializeAs="String">
        <value>White</value>
      </setting>
    </DHI.Solutions.Shell.Properties.Settings>
  </applicationSettings>
  <appSettings>
    <add key="PluginSearchPattern" value="DHI.Services*" />
    <add key="EnableWindowsFormsHighDpiAutoResizing" value="true" />
  </appSettings>
  <userSettings>
    <DHI.Solutions.Shell.Properties.Settings>
      <setting name="WindowGeometry" serializeAs="String">
        <value />
      </setting>
    </DHI.Solutions.Shell.Properties.Settings>
  </userSettings>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="IronPython" publicKeyToken="7f709c5b713576e1" Culture="neutral" />
        <bindingRedirect oldVersion="*******-*******" newVersion="*******" />
        <publisherPolicy apply="no" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="IronPython.Modules" publicKeyToken="7f709c5b713576e1" Culture="neutral" />
        <bindingRedirect oldVersion="*******-*******" newVersion="*******" />
        <publisherPolicy apply="no" />
      </dependentAssembly>
     <dependentAssembly>
        <assemblyIdentity name="Microsoft.Dynamic" publicKeyToken="7f709c5b713576e1" Culture="neutral" />
        <bindingRedirect oldVersion="*******-*******" newVersion="*******" />
        <publisherPolicy apply="no" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Scripting" publicKeyToken="7f709c5b713576e1" Culture="neutral" />
        <bindingRedirect oldVersion="*******-*******" newVersion="*******" />
        <publisherPolicy apply="no" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="1*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Numerics.Vectors" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.ValueTuple" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="NetTopologySuite" publicKeyToken="f580a05016ebada1" culture="neutral" />
        <codeBase version="1.14.0.0" href="ThinkGeo\NetTopologySuite.dll" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="GeoAPI" publicKeyToken="a1a0da7def465678" culture="neutral" />
        <codeBase version="1.7.4.0" href="ThinkGeo\GeoAPI.dll" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="MathNet.Numerics" culture="neutral" />
        <codeBase version="4.15.0.0" href="MPC\MathNet.Numerics.dll" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>
