﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <appSettings>
    <add key="DebugTracePath" value="%temp%\dhidss\%appname%-%pid%.svclog" />
    <add key="PerfomanceTracePath" value="%temp%\dhidss\performance-%pid%.svclog" />
    <add key="DebugTraceLevel" value="Error" />
    <add key="PerformanceTraceLevel" value="Off" />
    <add key="KeepDebugLogFilePeriod" value="6" />
    <add key="TraceConfig" value="x" />
    <add key="DebugTraceMaxLogFileSize" value="10" />
    <!-- in Mb -->
    <add key="XThreadSafe" value="false" />
    <add key="InformationFormStayTime" value="5000" />
    <add key="ApplicationName" value="MIKE Workbench" />
    <add key="StartPageURL" value="http://smaweb.extranet.dhigroup.com/SitePages/ClientInfo.aspx?dongle=id&amp;app=mo" />
    <add key="EnableStartPage" value="True" />
    <add key="IconURL" value="" />
    <add key="ForceCulture" value="" />
    <add key="UserInitiatedSave" value="True" />
    <add key="RunMode" value="1" />
    <add key="SplashBackgroundImagePath" value="" />
    <add key="DefaultText1" value="MIKE Workbench" />
    <add key="DefaultText2" value="Version: 2022.1" />
    <add key="DefaultText1Font" value="Tahoma, 8pt, style=Bold" />
    <add key="DefaultText1Color" value="ControlText" />
    <add key="DefaultText2Font" value="Tahoma, 8pt, style=Regular" />
    <add key="DefaultText2Color" value="ControlText" />
    <add key="DssCacheRetainPeriod" value="20" />
    <add key="DssCacheFlushFrequency" value="30" />
    <add key="DssCacheMinimumAvailableMemory" value="200" />
    <!-- in MB -->
    <add key="DssMaximum32BitProcessSize" value="1200" />
    <!-- in MB -->
    <add key="DssCacheLocation" value="" />
    <!-- leave empty for %TEMP%\DHIDSS -->
    <add key="DssDefaultExplorers" value="TimeseriesExplorer;GISExplorer;SpreadsheetExplorer;ToolsExplorer" />
    <add key="DisableMainMenu" value="False" />
    <add key="DisableHelp" value="False" />
    <add key="EnablePropertyWindows" value="True" />
    <add key="ShowSplash" value="True" />
    <add key="DisableStatusBar" value="False" />
    <add key="UIFont" value="Segoe UI, 8pt, style=Regular" />
    <add key="DBReconnectInterval" value="2000" />
    <!-- in milliseconds -->
    <add key="DBReconnect" value="False" />
    <add key="ShowToolLoadErrorsAsInformation" value="True" />
  </appSettings>
</configuration>