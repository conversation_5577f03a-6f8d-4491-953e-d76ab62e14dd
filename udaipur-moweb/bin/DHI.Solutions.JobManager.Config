﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
  <appSettings>
    <add key="GenerateJobLogFile" value="false" />
    <add key="RuntimeConfig" value="" />
    <add key="ServicePort" value="8089" />
    <add key="DebugService" value="true" />
    <add key="ServiceLogFilesLocation" value="" />
    <add key="ServiceLogFilesMaxMBSize" value="1" />
    <add key="ServiceLogFilesMaxHours" value="4" />
    <add key="ServiceLogFilesMaxCount" value="10" />
    <!-- ReportOnEndpointnotFound 0: no reporting, 1: information message, 2: recoverable exception, 4: both -->
    <add key="ReportOnEndpointNotFound" value="1" />

    <!-- AllowList, DisallowList: Allow/Disallow semi-colon seperated list of users to execute jobs. Note string shall start and end with a ';'
             Note: Empty AllowList will allow all users to execute -->
    <!--
        <add key="AllowList" value=";admin;" />
        <add key="DisallowList" value=";xyzxyz;" />
        -->

    <!-- Force job service to execute using trusted account (sso> -->
    <add key="UseTrustedAccount" value="false" />

    <!-- Override job connection string with the one specified here -->
    <!--
        <add key="Connection" value="database=$(DB);host=$(HOST);port=5432;dbflavour=PostgreSQL" />
        -->
    <!-- Specify folder for job log files (jobrunner logs and service execution log -->
    <!-- <add key="LogFolder" value="c:\temp\joblog" /> -->
  </appSettings>
</configuration>