<?xml version="1.0" encoding="utf-8" ?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <!-- $Id: MSBuild.Community.Tasks.Targets & DHI.Solutions.JobManager.Tasks.Targets 303 2007-02-23 15:49:46Z pwelter34 $ -->
  <PropertyGroup>
    <DSSTasksLib>$(DSSBIN)\DHI.Solutions.JobManager.Tasks.dll</DSSTasksLib>
    <MSBuildTaskLib>$(MSBuildToolsPath)\Microsoft.Build.Tasks.v4.0.dll</MSBuildTaskLib>
  </PropertyGroup>
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.DownloadUSGSDailyValues" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.AppendTimeseries" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.ApproveSimulation" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.CalculateTimeseriesQuantile" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.CloneScenario" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.CopySpreadsheet" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.CopyTimeseries" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.CopyTimeseriesByGroup" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.CopyTimeseriesByGroupPeriod" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.CopyTimeseriesByPeriod" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.CreateTimeseriesGroup" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.DSSTaskBase" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.ExistsSimulation" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.ExistsTimeseries" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.ExistsTimeseriesGroup" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.ExistTimeseriesValues" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.ExportTimeseries" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.GetScenarioInfo" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.ImportTimeseries" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.ImportTimeseriesFromDFS0" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.InsertTimeseriesValues" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.ManageChangeLog" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.ManageEventLog" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.ManageDataBrokerLog" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.ManageJobLogs" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.ManageSimulations" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.MoveTimeseries" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.RemoveTimeseries" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.RemoveTimeseriesGroup" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.RemoveSimulation" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.RenameSimulation" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.RenameTimeseries" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.ResampleTimeseries" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.RunScenario" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.RunScript" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.RunToolHierarchy" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.RunToolSequence" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.SaveSpreadsheetTimeseries" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.SimulationJobInstanceLog" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.SmoothTimeseries" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.SumTimeseries" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.TrimTimeSeries" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.UpdateSpreadsheet" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.MakeTimeStamp" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.GetTimeStamp" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.SetTimeStamp" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.SetTimeStamp" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.JobHelper" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.GetWorkspaceSetting" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.SetWorkspaceSetting" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.SaveBlob" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.MakeZip" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.UnzipFile" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.Sleep" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.GetCultureName" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.CreateScenarioInputTimeseries" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.TagCurrentJob" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.TimeseriesStatistics" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.FinalizeModel" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.InitializeModel" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.RunModel" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.Db2File" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.TimeseriesFactory" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.File2Db" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.CopyDirectory" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.DateTimes" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.RunJob" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.ExportDocument" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.DownloadFiles" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.RasterCalculator" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.RasterReclassification" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.RasterProject" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.RasterConversion" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.DFSWrite" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.CheckFTPFile" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.DFSMerge" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.GFSDownloader" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.GFSBuilder" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.DfsuToDfs2" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.RunWorkflow" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.RemoveRasterTimeSteps" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.RemoveTimeseriesValues" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.Vacuum" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.GenerateReport" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.ExitIfRunning" />
  <UsingTask AssemblyFile="$(DSSTasksLib)" TaskName="DHI.Solutions.JobManager.Tasks.ManageInitialConditions" />

  <UsingTask AssemblyFile="$(MSBuildTaskLib)" TaskName="CallTarget" />
  <UsingTask AssemblyFile="$(MSBuildTaskLib)" TaskName="CombinePath" />
  <UsingTask AssemblyFile="$(MSBuildTaskLib)" TaskName="ConvertToAbsolutePath" />
  <UsingTask AssemblyFile="$(MSBuildTaskLib)" TaskName="Copy" />
  <UsingTask AssemblyFile="$(MSBuildTaskLib)" TaskName="CreateProperty" />
  <UsingTask AssemblyFile="$(MSBuildTaskLib)" TaskName="Delete" />
  <UsingTask AssemblyFile="$(MSBuildTaskLib)" TaskName="Error" />
  <UsingTask AssemblyFile="$(MSBuildTaskLib)" TaskName="Exec" />
  <UsingTask AssemblyFile="$(MSBuildTaskLib)" TaskName="FindInList" />
  <UsingTask AssemblyFile="$(MSBuildTaskLib)" TaskName="FindUnderPath" />
  <UsingTask AssemblyFile="$(MSBuildTaskLib)" TaskName="MakeDir" />
  <UsingTask AssemblyFile="$(MSBuildTaskLib)" TaskName="Message" />
  <UsingTask AssemblyFile="$(MSBuildTaskLib)" TaskName="ReadLinesFromFile" />
  <UsingTask AssemblyFile="$(MSBuildTaskLib)" TaskName="RemoveDir" />
  <UsingTask AssemblyFile="$(MSBuildTaskLib)" TaskName="RemoveDuplicates" />
  <UsingTask AssemblyFile="$(MSBuildTaskLib)" TaskName="Warning" />
  <UsingTask AssemblyFile="$(MSBuildTaskLib)" TaskName="WriteLinesToFile" />
</Project>