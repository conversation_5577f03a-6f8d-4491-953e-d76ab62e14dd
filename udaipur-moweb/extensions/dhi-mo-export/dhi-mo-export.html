<dom-module id='check-boxes'>
    <template>
        <style include='iron-flex iron-flex-alignment'>
            paper-checkbox {
                --paper-checkbox-unchecked-color: #005685;
                --paper-checkbox-unchecked-ink-color: #005685;
                --paper-checkbox-checked-color: #005685;
                --paper-checkbox-checked-ink-color: #005685;
                --paper-checkbox-label-color: #005685;
                --paper-checkbox-vertical-align: top;
            }

            .fontbase {
                color: #005685;
                @apply(--paper-font-common-nowrap);
                @apply(--paper-font-subhead);
                @apply(--paper-input-container-label);
                @apply(--paper-transition-easing);
            }
        </style>
        <div id='exportboxes' class='vertical layout'></div>    
        <paper-icon-button title='Close' icon='icons:close' style='position:absolute; right:5px; top:5px;' on-tap='onClose'></paper-icon-button>
    </template>
</dom-module>
<script>
    Polymer({
        is: 'check-boxes',

        properties: {
            node: {
                type: Array,
                values: function() { return []; },
                observer: '_nodeChanged'
            }
        },

        _nodeChanged: function() {
            this.renderNodes(this.node, 0);
        },

        renderNodes: function (nodes, level) {
            nodes.map(function(node) {
                var dynamicEl = document.createElement("paper-checkbox");
                dynamicEl.setAttribute('id', node.Id);
                dynamicEl.setAttribute('type', 'filtergroup');

                dynamicEl.children[1].textContent = node.Name;
                dynamicEl.style.paddingBottom = '5px';
                dynamicEl.style.paddingLeft = (level * 10).toString() +  'px';
                // this.listen(dynamicEl, 'change', 'change');

                this.$$('#exportboxes').appendChild(dynamicEl);
                level++;                
                this.renderNodes(node.Children, level);
                level--;
            }, this);
        },

        getSelected: function() {
            var items = this.$$('#exportboxes').children;
            
            return toArray(items).filter(function(node){
                return node.value.checked == true;
            }).map(function(node){
                return node.value.id;
            });
        }
    });
</script>

<dom-module id='dhi-mo-export'>
    <style include='iron-flex iron-flex-alignment'>
        paper-checkbox {
            --paper-checkbox-unchecked-color: #005685;
            --paper-checkbox-unchecked-ink-color: #005685;
            --paper-checkbox-checked-color: #005685;
            --paper-checkbox-checked-ink-color: #005685;
            --paper-checkbox-label-color: #005685;
            --paper-checkbox-vertical-align: top;
        }

        .fontbase {
            color: #005685;
            @apply(--paper-font-common-nowrap);
            @apply(--paper-font-subhead);
            @apply(--paper-input-container-label);
            @apply(--paper-transition-easing);
        }

        paper-button {
            background: #005685;
            color: #fff;
        }

        dhi-date-picker-input {
            --dhi-date-picker-input-color: #0D3958;
        }
    </style>
    <template>
        <div class='vertical layout'>
            <div class='fontbase' style='padding-left:20px; padding-top:20px; font-size:15px; font-weight:bold'>Export Time Series</div>
            <div class='horizontal layout' style='padding-left:20px; padding-top:10px'>
                <div>
                    <div class='fontbase' style='font-size:15px; padding-bottom:10px'>Select Filter Groups</div>
                    <div id='filterGroupsContainer' style='overflow:auto' >
                        <check-boxes id='filterGroupsCheckBoxes' node='{{filterGroupsDisplay}}'></check-boxes>
                    </div>
                </div>
                <div class='vertical layout' style='padding-left:20px'>
                    <div class='fontbase' style='font-size:15px; padding-bottom:10px'>Select Feature Types</div>
                    <template is='dom-repeat' items='{{featureTypes}}'>
                        <paper-checkbox id='{{item.Id}}' type='featuretype' style='padding-bottom:5px' checked='{{item.Checked}}'>{{item.Name}}</paper-checkbox>
                    </template>
                </div>
                <div class='vertical layout' style='padding-left:20px'>
                    <div class='fontbase' style='font-size:15px; padding-bottom:10px'>Export Period</div>
                    <div class='vertical layout' style='width:200px'>
                        <paper-radio-group selected="observationRadioButton">
                            <paper-radio-button on-change='radioChanged' name='observationRadioButton'>Use observation period</paper-radio-button>
                            <paper-radio-button on-change='radioChanged' name='selectRadioButton'>Select period</paper-radio-button>
                        </paper-radio-group>                        
                    </div>
                    <div class='horizontal layout' style='padding-top:0px;padding-left:40px;'>
                        <div>
                            <dhi-date-picker-input disabled$="[[pickersDisabled]]" id='pickerStartTime' caption='Start Time' error-message='Invalid date' date-time='{{startTime}}'></dhi-date-picker-input>
                        </div>
                    </div>
                    <div class='horizontal layout' style="padding-left:40px;">
                        <div>
                            <dhi-date-picker-input disabled$="[[pickersDisabled]]" id='pickerEndTime' caption='End Time'error-message='Invalid date' date-time='{{endTime}}'></dhi-date-picker-input>
                        </div>
                    </div>
                </div>
            </div>
            <div class='horizontal layout'>
                <div class='flex'></div>
                <paper-button on-tap='onExport' id='csv'>csv</paper-button>
                <paper-button on-tap='onExport' id='dfs0'>dfs0</paper-button>
            </div>
        </div>
        <paper-icon-button title='Close' icon='icons:close' style='position:absolute; right:5px; top:5px;' on-tap='onClose'></paper-icon-button>
    </template>
</dom-module>
<script>
    Polymer({
        is: 'dhi-mo-export',

        properties: {
            apiUrl: {
                type: String,
                value: ''
            },

            apiId: {
                type: String,
                value: ''
            },

            filterGroups: {
                type: Array,
                value: function() { return []; }
            },

            featureTypes: {
                type: Array,
                value: function() { return []; }
            },

            featureType: {
                type: String,
                value: ''
            },

            documentConnection: {
                type: String,
                value: ''
            },

            isReady: {
                type: Boolean,
                value: false
            }, 

            startTime: {
                type: Object,
                value: function() { return {}; },
                observer: '_dateChanged'                
            },

            endTime: {
                type: Object,
                value: function() { return {}; },
                observer: '_dateChanged'                
            },

            pickersDisabled: {
                type: Boolean,
                value: true
            }
        },

        onClose: function() {
            this['close']();
        },

        attached: function() {
            this.isReady = true;

            if (this.configuration.Metadata.FilterGroups) {
                this.filterGroupsDisplay = this.setFilterGroups();
            }

            var featureTypes = [];
            if (this.theme) {
                for (var i = 0; i < this.theme.FeatureTypes.length; i++) {
                    if (this.theme.FeatureTypes[i].LayerType === 'DynamicFeatureLayer') {
                        featureTypes.push({
                            Id: this.theme.FeatureTypes[i].Id,
                            Name: this.theme.FeatureTypes[i].Name,
                            Checked: this.featureType == this.theme.FeatureTypes[i].Id
                        });
                    }
                }
            }
            this.featureTypes = featureTypes;

            this.startTime = moment().format(iso8601);
            this.endTime = moment().add(6, 'hours').format(iso8601);

            var that = this;
            window.addEventListener('resize', function(e) {
                that.setSize();
            });
            this.setSize();
        },

        createTree: function(list, nodeUnchecked) {
            var tree = [];
            var mappedList = {};
            var mappedElement;

            for(var i = 0, len = list.length; i < len; i++) {
                var element = list[i];
                element.Checked = this.filterGroups.indexOf(element.Id) > -1;
                mappedList[element.Id] = element;
                mappedList[element.Id].Children = [];
            }

            for (var id in mappedList) {
                if (mappedList.hasOwnProperty(id)) {
                    mappedElement = mappedList[id];
                    mappedElement.Checked = this.filterGroups.indexOf(mappedElement.Id) > -1;
                    if (mappedElement.ParentItemId != 'undefined') {
                        if (mappedElement.Id == nodeUnchecked) {
                            mappedElement.NodeUnchecked = true;
                        } else {
                            if (mappedList[mappedElement.ParentItemId].NodeUnchecked) {
                                mappedElement.NodeUnchecked = true;
                            } else if (mappedList[mappedElement.ParentItemId].Checked) {
                                mappedElement.Checked = true;
                                if (this.filterGroups.indexOf(mappedElement.Id) != -1) {
                                    this.splice('filterGroups', this.filterGroups.indexOf(mappedElement.Id), 1);
                                }
                            }
                        }

                        mappedList[mappedElement.ParentItemId].Children.push(mappedElement);
                    } else {
                        tree.push(mappedElement);
                    }
                }
            }
            return tree;
        },

        setFilterGroups: function(nodeUnchecked) {
            var filterGroups = [];
            for (var i = 0; i < this.configuration.Metadata.FilterGroups.length; i++) {
                filterGroups.push({
                    Id: this.decodeHtml(this.configuration.Metadata.FilterGroups[i].Id),
                    Name: this.decodeHtml(this.configuration.Metadata.FilterGroups[i].Name),
                    ParentItemId: this.decodeHtml(this.configuration.Metadata.FilterGroups[i].ParentItemId)
                });
            }

            var tree = this.createTree(filterGroups, nodeUnchecked);
            return tree;
        },

        decodeHtml: function (html) {
            var txt = document.createElement("textarea");
            txt.innerHTML = html;
            return txt.value;
        },

        radioChanged: function(e) {
            this.pickersDisabled = !this.pickersDisabled;
        },

        _dateChanged: function(e) {
            // validation here
        },

        onExport: function(e) {
            var exportType = e.currentTarget.id;
            var featureTypes = [];
            var items = Polymer.dom(this.root).querySelectorAll('paper-checkbox');
            for (var i = 0; i < items.length; i++) {
                if (items[i].checked) {
                    if (items[i].getAttribute('type') == 'featuretype') {
                        featureTypes.push(items[i].id);
                    }
                }
            }

            var filterGroups = this.$.filterGroupsCheckBoxes.getSelected();

            var url = this.apiUrl 
                + '/api/document/' + this.documentConnection 
                + '/' + this.apiId + ';featuretypes=' + featureTypes.join('$') 
                + ';exporttype=' + exportType 
                + ';filtergroups=' + filterGroups.join('$');

            if(!this.pickersDisabled) {
                url = url   + ';fromdate=' + moment(this.startTime).format(iso8601NoColons)
                            + ';todate=' + moment(this.endTime).format(iso8601NoColons);                    
            }

            window.open(url);
        },

        setSize: function() {
            var height = window.innerHeight - 195;
            this.$.filterGroupsContainer.style.height = height + 'px';
        }
    });
</script>