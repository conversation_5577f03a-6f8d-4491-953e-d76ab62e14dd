<dom-module id='dhi-mo-jobs'>
    <style include='iron-flex iron-flex-alignment'>
        paper-spinner {
            --paper-spinner-layer-1-color: #005685;
            --paper-spinner-layer-2-color: #005685;
            --paper-spinner-layer-3-color: #005685;
            --paper-spinner-layer-4-color: #005685;
        }

    </style>


    <template>
        <iron-ajax id='jobAjax' url='{{jobListUrl}}' content-type='application/json; charset=utf-8' handle-as='json' on-response='getJobListHandler'></iron-ajax>
        <iron-ajax id='jobLogAjax' url='{{jobLogUrl}}' content-type='application/json; charset=utf-8' handle-as='json' on-response='getJobLogHandler'></iron-ajax>

            <div class='vertical layout' style='padding-left:20px; padding-top:20px'>
                <div class='fontbase' style='font-size:15px; font-weight:bold'>{{title}}</div>
                <div class='fontbase' style='font-size:15px; padding-bottom:10px'>{{subTitle}}</div>
                <div>
                    <div class='horizontal layout'>
                        <!--Left side: List with dates of job instances-->
                        <div horizontal style='width:200px' id='toc'>
                            <div id="scroll-list" style="display:block;height:400px;overflow-y: auto">
                                <x-element scroll-target="scroll-list">
                                    <dhi-warning-list data='{{jobData}}'
                                                      name-property='name'
                                                      value-property='value'
                                                      color-property='color'
                                                      text-color-property='textColor'
                                                      icon-property='icon'
                                                      selected-item='{{selectedDate}}'
                                                      box-shadow-selected='0 0 0 3px #FF00FF inset'
                                                      on-tap="_onTap">
                                    </dhi-warning-list>
                                </x-element>
                            </div>
                        </div>
                        <!--Right side (opens on-tap on right list): table with job instance log-->
                        <div horizontal id='table' class='flex layout'>
                            <div style='text-align:center; position:absolute; z-index:1000; top:50%; left:50%; margin-top:-16px; margin-left:-16px'>
                                <paper-spinner id='spinner'></paper-spinner>
                            </div>

                            <div id="scrollable-element" style="display:block; height:400px;overflow-y: auto;">
                                <x-element scroll-target="scrollable-element">
                                    <template is='dom-if' if='{{tableData}}'>
                                        <dhi-data-table style='border-bottom:1px solid lightgray; border-left:1px solid lightgray; border-right:1px solid lightgray; margin-top:2px;'
                                                        data='{{tableData}}'
                                                        base-class=''
                                                        no-ink>
                                        </dhi-data-table>
                                    </template>
                                </x-element>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            <paper-icon-button title='Close' icon='icons:close' style='position:absolute; right:5px; top:5px;' on-tap='onClose'></paper-icon-button>
    </template>
</dom-module>

<script>

    Polymer({
        is: 'dhi-mo-jobs',

        properties: {
            displayTitle: {
                type: String,
                value: "Job instances"
            },
            displaySubtitle: {
                type: String,
                value: "Select a job instance to the left for inspection."
            },
            displayNoLogData:{
                type: String,
                value: "No log available for job."
            },
            apiUrl: {
                type: String,
                observer: '_initChanged'
            },
            apiId: {
                type: String,
                observer: '_initChanged'
            },
            jobName: {
                type: String,
                observer: '_initChanged'
            },
            timestepConnection: {
                type: String,
                observer: '_initChanged'
            },
            jobListUrl: {
                type: String
            },
            jobLogUrl: {
                type: String
            },
            jobData: {
                type: Object,
                value: function () { return []; }
            },
            tableData: {
                type: Array,
                value: function () { return null }
            },
            dateTimeFormat:{
                type: String,
                value: "DD-MM-YYYY HH:mm:ss"
            },
            settings: {
                type: Object,
                value: function () { return []; },
                observer: '_settingsChanged'
            }
        },
        _settingsChanged: function () {
            if (this.settings) {
                if (this.settings.connections && this.settings.connections.TimeStep) {
                    this.timestepConnection = this.settings.connections.TimeStep;
                }
                if (this.settings.dateTimeFormat) {
                    this.dateTimeFormat = this.settings.dateTimeFormat;
                }
                if (this.settings.reflectedElements) {
                    var setting = this.getMySettings(this.settings.reflectedElements);
                    if (setting) {
                        if (setting.displayTitle) {
                            this.title = setting.displayTitle;
                        }
                        if (setting.displaySubtitle) {
                            this.subTitle = setting.displaySubtitle;
                        }
                    }
                }
            }
        },
        getMySettings: function (reflectedElements) {
            for (var i = 0; i < reflectedElements.length; i++) {
                var setting = reflectedElements[i];
                if (setting.elementName == "dhi-mo-jobs") {
                    return setting;
                }
            }
        },
        _onTap: function (e) {
            if (this.apiUrl != null && this.apiId && this.jobName) {
                if (e.model && e.model.item && e.model.item.value) {
                    var date = e.model.item.value
                    this.$.spinner.active = true;
                    var selDate = date._i;
                    selDate = selDate.replaceAll(':', '');
                    this.jobLogUrl = this.apiUrl + '/api/timestep/' + this.timestepConnection + '/' + this.apiId + ';Type=jobinstance;Id=' + this.jobName + '/data/' + selDate;
                    this.$.jobLogAjax.generateRequest();
                }
            }
        },
        _initChanged: function(){
            if (this.apiUrl != null && this.apiId && this.jobName && this.timestepConnection) {
                this.jobListUrl = this.apiUrl + '/api/timestep/' + this.timestepConnection + '/' + this.apiId + ';Type=jobinstancelist;Id=' + this.jobName + '/data/2000-01-01T000000';
                this.$.jobAjax.generateRequest();
            }
        },
        getJobLogHandler: function(header, data) {
            if (data.xhr.status === 200) {
                if (data.xhr.response) {
                    var targets = toObject(data.xhr.response);
                    var targetData = [];
                    var activeWidth = ';width:20px';
                    var taskWidth = ';width:100%';
                    var topBorder = ';border-top:1px solid lightgray'
                    var leftBorder = ';border-left:1px solid lightgray'
                    var style = 'align-self:flex-start' + topBorder + '; overflow:hidden';
                    var styleHeading = 'font-weight:bold;' + topBorder + '; overflow:hidden';
                    var empty = {
                        data: [
                           { value: "", style: style + activeWidth },
                           { value: this.displayNoLogData, style: styleHeading + taskWidth }
                        ]
                    };

                    if (!targets) {
                        targetData.push(empty);
                    }
                    else
                    {
                        for (i = 0; i < targets.length; i++) {
                            var target = targets[i];
                            var tasks = target.Tasks;
                            if (!tasks) {
                                targetData.push(empty);
                            }
                            else
                            {
                                for (j = 0; j < tasks.length; j++) {
                                    var task = tasks[j];
                                    var firstCell = { showIcon: true, icon: 'report-problem', style: style + activeWidth + ';color:#ee0000' };
                                    if (task.Status && task.Status == "Succeeded") {
                                        firstCell = { showIcon: true, icon: 'done', style: style + activeWidth + ';color:#00aa00' };

                                    }
                                    var elapsedTime = "";
                                    if (task.ElapsedTime) {
                                        elapsedTime = " (" + task.ElapsedTime + ")"
                                    }
                                    targetData.push({
                                        data: [
                                           firstCell,
                                           { value: task.TaskName + elapsedTime, style: styleHeading + taskWidth + leftBorder, tooltip: task.TaskName + elapsedTime }
                                        ]
                                    });

                                    var messages = task.Messages;
                                    if (messages && messages.length > 0) {
                                        for (var k = 0; k < messages.length; k++) {
                                            targetData.push({
                                                data: [
                                                   { value: "", style: style + activeWidth },
                                                   { value: messages[k], style: style + taskWidth + leftBorder, tooltip: messages[k]}
                                                ]
                                            });
                                        }
                                    }
                                }
                            }
                        }
                    }

                    this.tableData = targetData;
                    this.$.spinner.active = false;
                }
            }
        },
        getJobListHandler: function (header, data) {
            if (data.xhr.status === 200) {
                if (data.xhr.response) {

                    var response = toObject(data.xhr.response);
                    var jobInstances = response[this.jobName];
                    var jobList = [];
                    if (jobInstances) {
                        for (var date in jobInstances) {
                            var icon = 'report-problem';
                            if (jobInstances[date].Status && jobInstances[date].Status == "Succeeded") {
                                icon = '';
                            }

                            jobList.push(
                            {
                                name: moment(date).format(this.dateTimeFormat),
                                value: moment(date),
                                color: '#005685',
                                textColor: '#FFFFFF',
                                icon: icon
                            });
                        }
                    }

                    this.jobData = jobList;


                }
            }
            else {
                //this.data.length = 0;
                alert('EXCEPTION: ' + data.xhr.responseText);
            }
        },
        onClose: function() {
            this['close']();
        },
    });
</script>
