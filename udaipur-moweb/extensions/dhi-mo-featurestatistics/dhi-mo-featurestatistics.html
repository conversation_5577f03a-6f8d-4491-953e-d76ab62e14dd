<!--Assumption: Reference to below will be included in the file using below element.
<link rel='import' href='bower_components/iron-flex-layout/iron-flex-layout-classes.html'>
<link rel='import' href='dhi_elements/dhi-javascripts/dhi-javascripts.html'>
<link rel='import' href='dhi_elements/dhi-data-table/dhi-data-table.html'>-->

<dom-module id='dhi-mo-featurestatistics'>
  <style include='iron-flex iron-flex-alignment'>
    dhi-data-table {
      --dhi-data-table-color: var(--dhi-mo-featurestatistics-color, #1976D2);
    }

    .myDiv {
      position: absolute;
      z-index: 9;
      background-color: #f1f1f1;
      border: 1px solid #e6d8d8;
      width: auto;
      text-align: center;
    }

    .title {
      padding: 5px;
      cursor: move;
      z-index: 10;
      background-color: #2196F3;
      color: #fff;
    }
  </style>
  <template>
    <div class="myDiv" id="myDiv" style="top:50px;left:50px">
      <div class='title'>{{title}}</div>
      <dhi-data-table data='{{featureStatistics}}' allow-csv-download='{{allowCsvDownload}}'></dhi-data-table>
      <button on-tap='closeTap' style='border: none;padding: 5px 5px;'>Close</button>
    </div>
  </template>
</dom-module>
<script>
  Polymer({
    is: 'dhi-mo-featurestatistics',
    properties: {
      data: {
        type: Array,
        observer: '_dataChanged'
      },
      title: {
        type: String,
        value: ''
      },
      allowCsvDownload: {
        type: Boolean,
        value: false
      }
    },
    ready: function () {
      this.dragElement(this.$$(".myDiv"));
    },
    closeTap: function () {
      this.fire("onClose");
      this.parentNode.removeChild(this);
    },
    _dataChanged: function (newValue, oldValue) {
      if (!newValue) {
        this.featureStatistics = null;
        return;
      }
      var processedData = [];
      var tableLineColor = '#005685';
      var rightEdgeCellTemplate = {
        'style': 'border-bottom:1px solid ' + tableLineColor + '; width:60px; overflow:hidden; white-space:nowrap; text-overflow:ellipsis;'
      };
      var bottomLeftCornerCellTemplate = {
        'style': 'border-right:1px solid ' + tableLineColor + '; width:60px; overflow:hidden; white-space:nowrap; text-overflow:ellipsis;'
      };
      var bottomEdgeCellTemplate = {
        'style': 'border-right:1px solid ' + tableLineColor + '; width:60px; overflow:hidden; white-space:nowrap; text-overflow:ellipsis;'
      };
      var bottomRightCornerTemplate = {
        'style': 'width:50px; overflow:hidden; white-space:nowrap; text-overflow:ellipsis;'
      };
      var cellTemplate = {
        'style': 'border-bottom:1px solid ' + tableLineColor + '; border-right:1px solid ' + tableLineColor +
          '; width:50px; overflow:hidden; white-space:nowrap; text-overflow:ellipsis;'
      };
      for (var i = 0; i < newValue.length; i++) {
        var row = newValue[i];
        if (row.length > 0) {
          var rowToAdd = {
            data: []
          };
          for (var j = 0; j < row.length; j++) {
            var cellToAdd;
            if (j == (row.length - 1) && i == (newValue.length - 1)) {
              cellToAdd = clone(bottomRightCornerTemplate);
            } else if (j == row.length - 1) {
              cellToAdd = clone(rightEdgeCellTemplate);
            } else if (j == 0 && i == (newValue.length - 1)) {
              cellToAdd = clone(bottomLeftCornerCellTemplate);
            } else if (i == (newValue.length - 1)) {
              cellToAdd = clone(bottomEdgeCellTemplate);
            } else {
              cellToAdd = clone(cellTemplate);
            }

            if (i == 0 || j == 0) {
              cellToAdd.style += ' font-weight: bold;';
            }
            cellToAdd.value = ((row[j] == undefined || row[j] == null) || (i == 0 && j == 0)) ? '' : row[j];
            if (cellToAdd.value !== '' && !isNaN(cellToAdd.value)) {
              cellToAdd.rightAlign = true;
            }
            rowToAdd.data.push(cellToAdd);
          }
          processedData.push(rowToAdd);
        }
      }
      this.featureStatistics = processedData;

    },
    dragElement: function (elmnt) {
      var pos1 = 0,
        pos2 = 0,
        pos3 = 0,
        pos4 = 0;
      if (document.getElementById(elmnt.id + "header")) {
        // if present, the header is where you move the DIV from:
        document.getElementById(elmnt.id + "header").onmousedown = dragMouseDown;
      } else {
        // otherwise, move the DIV from anywhere inside the DIV:
        elmnt.onmousedown = dragMouseDown;
      }

      function dragMouseDown(e) {
        e = e || window.event;
        e.preventDefault();
        // get the mouse cursor position at startup:
        pos3 = e.clientX;
        pos4 = e.clientY;
        document.onmouseup = closeDragElement;
        // call a function whenever the cursor moves:
        document.onmousemove = elementDrag;
      }

      function elementDrag(e) {
        e = e || window.event;
        e.preventDefault();
        // calculate the new cursor position:
        pos1 = pos3 - e.clientX;
        pos2 = pos4 - e.clientY;
        pos3 = e.clientX;
        pos4 = e.clientY;
        // set the element's new position:
        elmnt.style.top = (elmnt.offsetTop - pos2) + "px";
        elmnt.style.left = (elmnt.offsetLeft - pos1) + "px";
      }

      function closeDragElement() {
        // stop moving when mouse button is released:
        document.onmouseup = null;
        document.onmousemove = null;
      }
    }
  });
</script>