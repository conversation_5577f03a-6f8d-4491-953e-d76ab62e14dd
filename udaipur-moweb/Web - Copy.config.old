﻿<?xml version="1.0" encoding="utf-8"?>
<!--
  For more information on how to config ure your ASP.NET application, please visit
  http://go.microsoft.com/fwlink/?LinkId=301879
  -->
<configuration>
  <configSections>
    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <section name="DHI.Solutions.MOWeb.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    </sectionGroup>
  </configSections>
  <appSettings>
  </appSettings>
  <system.web>
    <compilation debug="true" targetFramework="4.5.1" />
    <httpRuntime targetFramework="4.5.1" maxUrlLength="5000" maxRequestLength="2147483647" relaxedUrlToFileSystemMapping="true" />
    <hostingEnvironment shadowCopyBinAssemblies="false" />
  </system.web>
  <applicationSettings>
    <DHI.Solutions.MOWeb.Properties.Settings>
      <setting name="UriDateTimeFormat" serializeAs="String">
        <value>yyyy-MM-ddTHHmmss</value>
      </setting>
      <setting name="IsoDateTimeFormat" serializeAs="String">
        <value>yyyy-MM-ddTHH:mm:ss</value>
      </setting>
      <setting name="WmsName" serializeAs="String">
        <value>WMS</value>
      </setting>
      <setting name="WmsTitle" serializeAs="String">
        <value>DHI Web Map Service</value>
      </setting>
      <setting name="LazyCreation" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="JobTimeout" serializeAs="String">
        <value>2.00:00:00</value>
      </setting>
      <setting name="CacheTimeSeriesTimeout" serializeAs="String">
        <value>00:00:00</value>
      </setting>
      <setting name="CacheAccountsTimeout" serializeAs="String">
        <value>00:00:00</value>
      </setting>
      <setting name="CacheGISTimeout" serializeAs="String">
        <value>00:00:00</value>
      </setting>
      <setting name="CompressionThresshold" serializeAs="String">
        <value>1024</value>
      </setting>
      <setting name="AccountActivationUri" serializeAs="String">
        <value>http://api.dhigroup.com/account/activation</value>
      </setting>
      <setting name="SmtpHost" serializeAs="String">
        <value>smtpserv.dhi.dk</value>
      </setting>
      <setting name="PasswordResetUri" serializeAs="String">
        <value>http://api.dhigroup.com/account/passwordreset</value>
      </setting>
      <setting name="TokenLifeTime" serializeAs="String">
        <value>1.00:00:00</value>
      </setting>
      <setting name="SmtpUsername" serializeAs="String">
        <value />
      </setting>
      <setting name="SmtpPassword" serializeAs="String">
        <value />
      </setting>
      <setting name="SmtpPort" serializeAs="String">
        <value>25</value>
      </setting>
      <setting name="SmtpSetCredentials" serializeAs="String">
        <value>False</value>
      </setting>
    </DHI.Solutions.MOWeb.Properties.Settings>
  </applicationSettings>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">      
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-12.0.3.0" newVersion="12.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.0.0" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http.Formatting" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Http" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Http.WebHost" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Http.Cors" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.AspNet.WebApi.Versioning" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.IO.RecyclableMemoryStream" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.0.1" newVersion="4.2.0.1" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.5.0" newVersion="4.0.5.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Numerics.Vectors" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.1.4.0" newVersion="4.1.4.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.ValueTuple" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="DHI.Generic.MikeZero.EUM" publicKeyToken="c513450b5d0bf0bf" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-19.1.0.0" newVersion="19.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="DHI.Generic.MIKEZero.DFS" publicKeyToken="c513450b5d0bf0bf" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-19.1.0.0" newVersion="19.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="DHI.Generic.DfsSelection.Constraints" publicKeyToken="c513450b5d0bf0bf" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-19.1.0.0" newVersion="19.0.0.0" />
      </dependentAssembly>
	  <dependentAssembly>
        <assemblyIdentity name="DHI.Chart.Map" />
        <bindingRedirect oldVersion="0.0.0.0-20.1.0.0" newVersion="20.1.0.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <system.webServer>
    <caching enabled="true" enableKernelCache="true">
      <profiles>
        <remove extension=".html" />
        <remove extension=".png" />
        <remove extension=".json" />
        <add extension=".html" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange"/>
        <add extension=".png" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange"/>
        <add extension=".json" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange"/>
      </profiles>
    </caching>
    <staticContent>
      <remove fileExtension=".json" />
      <mimeMap fileExtension=".json" mimeType="application/json" />
    </staticContent>
    <modules>
      <remove name="UrlRoutingModule-4.0" />
      <add name="UrlRoutingModule-4.0" type="System.Web.Routing.UrlRoutingModule" preCondition="" />
    </modules>
    <handlers>
      <remove name="ExtensionlessUrlHandler-Integrated-4.0" />
      <remove name="OPTIONSVerbHandler" />
      <remove name="TRACEVerbHandler" />
      <add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
    </handlers>
  </system.webServer>
  <system.runtime.caching>
    <memoryCache>
      <namedCaches>
        <add name="default" physicalMemoryLimitPercentage="50" pollingInterval="00:02:00" />
      </namedCaches>
    </memoryCache>
  </system.runtime.caching>
</configuration>


