{"$type": "System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[DHI.Services.IConnection, DHI.Services]], mscorlib", "mo-timestep": {"$type": "DHI.Services.TimeSteps.Web.TimeStepServiceConnection, DHI.Services.TimeSteps.Web", "ConnectionString": "runtimeconfig=OperationsWeb;host=localhost;port=5434;database=Udaipur;refreshinterval=300", "ServerType": "DHI.Services.Provider.MC.Operations.TimeStepServer, DHI.Services.Provider.MC.Operations", "Name": "MIKE OPERATIONS timestep connection", "Id": "mo-timestep"}, "mo-gis": {"$type": "DHI.Services.GIS.Web.GisServiceConnection, DHI.Services.GIS.Web", "ConnectionString": "runtimeconfig=OperationsWeb;host=localhost;port=5434;database=Udaipur;refreshinterval=0", "RepositoryType": "DHI.Services.Provider.MC.Operations.FeatureCollectionRepository, DHI.Services.Provider.MC.Operations", "Name": "MIKE OPERATIONS gis connection", "Id": "mo-gis"}, "mo-timeseries": {"$type": "DHI.Services.TimeSeries.Web.GroupedDiscreteTimeSeriesServiceConnection, DHI.Services.TimeSeries.Web", "ConnectionString": "runtimeconfig=OperationsWeb;host=localhost;port=5434;database=Udaipur;refreshinterval=0", "RepositoryType": "DHI.Services.Provider.MC.Operations.TimeSeriesRepository, DHI.Services.Provider.MC.Operations", "Name": "MIKE OPERATIONS time series connection", "Id": "mo-timeseries"}, "mo-doc": {"$type": "DHI.Services.Documents.Web.DocumentServiceConnection, DHI.Services.Documents.Web", "ConnectionString": "runtimeconfig=OperationsWeb;host=localhost;port=5434;database=Udaipur;refreshinterval=0", "RepositoryType": "DHI.Services.Provider.MC.Operations.DocumentRepository, DHI.Services.Provider.MC.Operations", "Name": "MIKE OPERATIONS document connection", "Id": "mo-doc"}, "mo-job": {"$type": "DHI.Services.Jobs.Web.JobServiceConnection, DHI.Services.Jobs.Web", "JobRepositoryConnectionString": "runtimeconfig=OperationsWeb;host=localhost;port=5434;database=Udaipur;refreshinterval=0", "JobRepositoryType": "DHI.Services.Provider.MC.Jobs.JobRepository, DHI.Services.Provider.MC.Jobs", "TaskRepositoryConnectionString": "runtimeconfig=OperationsWeb;host=localhost;port=5434;database=Udaipur;refreshinterval=0", "TaskRepositoryType": "DHI.Services.Provider.MC.Jobs.TaskRepository, DHI.Services.Provider.MC.Jobs", "Name": "MIKE OPERATIONS job connection", "Id": "mo-job"}, "dfs2-map": {"$type": "DHI.Services.GIS.Web.FileCachedMapServiceConnection, DHI.Services.GIS.Web", "MapSourceConnectionString": "", "MapSourceProperties": {"$type": "DHI.Services.Parameters, DHI.Services", "NumberOfCachedZoomLevels": 5, "CachedImageWidth": 2048, "CacheExpirationInMinutes": 360, "CacheRoot": "C:\\windows\\temp\\dhidss\\FileCacheMapServiceConnection"}, "MapSourceType": "DHI.Services.Provider.MIKECore.Dfs2MapSource, DHI.Services.Provider.MIKECore", "Name": "Dfs2 map connection", "Id": "dfs2-map"}, "dfsu-map": {"$type": "DHI.Services.GIS.Web.FileCachedMapServiceConnection, DHI.Services.GIS.Web", "MapSourceConnectionString": "", "MapSourceProperties": {"$type": "DHI.Services.Parameters, DHI.Services", "NumberOfCachedZoomLevels": 5, "CachedImageWidth": 2048, "CacheExpirationInMinutes": 360, "CacheRoot": "C:\\windows\\temp\\dhidss\\FileCacheMapServiceConnection"}, "MapSourceType": "DHI.Services.Provider.MIKECore.DfsuMapSource, DHI.Services.Provider.MIKECore", "Name": "Dfsu map connection", "Id": "dfsu-map"}, "particle-map": {"$type": "DHI.Services.GIS.Web.MapServiceConnection, DHI.Services.GIS.Web", "MapSourceConnectionString": "", "MapSourceProperties": {"$type": "DHI.Services.Parameters, DHI.Services", "NumberOfCachedZoomLevels": 4, "CachedImageWidth": 2048, "CacheExpirationInMinutes": 360}, "MapSourceType": "DHI.Services.Provider.MIKECore.ParticleMapSource, DHI.Services.Provider.MIKECore", "Name": "Particle map connection", "Id": "particle-map"}, "mo-traces": {"$type": "DHI.Services.TimeSteps.Web.TimeStepServiceConnection, DHI.Services.TimeSteps.Web", "ConnectionString": "[AppData]dfs2\\Forecast.dfs2", "ServerType": "DHI.Services.Provider.MIKE.Dfs2TimeStepServer, DHI.Services.Provider.MIKE", "Name": "Dfs2 traces connection", "Id": "mo-traces"}}