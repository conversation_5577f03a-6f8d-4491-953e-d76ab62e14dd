{"OPTIONALcenter": {"latitude": 46.00879, "longitude": 14.902405, "zoom": 8}, "OPTIONALbackgroundMapConfig": [{"type": "wms", "url": "https://mesonet.agron.iastate.edu/cgi-bin/wms/nexrad/n0r.cgi", "layers": "nexrad-n0r-900913"}], "selectionColor": "#0099ff", "OPTIONALapiUrl": "http://localhost/webapi", "OPTIONALgoogleAnalyticsCode": "UA-XXXXX-1", "OPTIONALconnections": {"TimeStep": "mo-timestep", "Gis": "mo-gis", "TimeSeries": "mo-timeseries", "Documents": "mo-doc", "Jobs": "mo-job"}, "useSingleConfigThemeDropDown": true, "showFilterGroups": true, "filterGroupCaption": "Select Groups", "stationSortProperty": "name", "OPTIONALdefaultFilterGroups": ["Filter1"], "OPTIONALcollapsibleLegend": true, "collapsibleLeftPanel": true, "leftPanelDefaultCollapsed": false, "chartPanelDefaultCollapsed": false, "jobStatusView": false, "OPTIONALconfigurationCaption": "Configuration", "OPTIONALthemeCaption": "Themes", "OPTIONALshowLayersList": true, "OPTIONALlayerCaption": "Layers", "OPTIONALstationCaption": "Stations", "OPTIONALobservationPeriodCaption": "Observation Period", "OPTIONALobservationPeriodGroupCaption": "Group", "OPTIONALobservationPeriodOffsetCaption": "Offset", "OPTIONALemailRequired": false, "OPTIONALforgotPassword": false, "OPTIONALforgotPasswordCaption": "", "OPTIONALuserNameCaption": "User name", "OPTIONALpasswordCaption": "Password", "OPTIONALrepeatPasswordCaption": "Repeat password", "OPTIONALloginCaption": "<PERSON><PERSON>", "OPTIONALemailOrUserIdCaption": "E-Mail address or User id", "OPTIONALreturnToLoginCaption": "Return to Login", "OPTIONALrequestPasswordResetCaption": "Request password reset", "OPTIONALchangePasswordCaption": "Change", "OPTIONALcancelChangePasswordCaption": "Cancel", "logo": "dhiLogo.png", "logoUrl": "http://www.dhigroup.com/", "title": "MIKE OPERATIONS WEB Showcase", "OPTIONALdateTimeFormat": "DD-MM-YYYY HH:mm", "OPTIONALdateTimeFormatShort": "DD-MM-YYYY", "OPTIONALTimeseriesTimeZone": "(UTC +01:00)", "OPTIONALlocale": "en", "OPTIONALlazyLoad": true, "OPTIONALlazyLoadCaption": "Loading", "OPTIONALlazyLoadBlockSize": 10, "OPTIONALthresholdNames": true, "OPTIONALthresholdDisplay": "required", "ribbon": true, "OPTIONALlogin": false, "OPTIONALloginAPIRoles": ["User", "Editor"], "OPTIONALloginMORoles": ["member", "webuser"], "OPTIONALloginExpiryDays": 1, "OPTIONALloginExpiryCaption": "login expiry", "successOverlay": "images/MIKEOPERATIONS/Overlays/ov_check.png", "failureOverlay": "images/MIKEOPERATIONS/Overlays/ov_error.png", "OPTIONALfeatureHover": [{"featureTypeId": "FT1", "showObservationPeriod": true}], "reflectedElements": [{"url": "extensions/dhi-mo-export/dhi-mo-export.html", "elementName": "dhi-mo-export", "OPTIONALavailableForConfigurationIds": ["Sava5"], "OPTIONALavailableForMORoles": ["member"], "displayName": "Export", "ribbonGroupName": "csv / dfs0", "icon": "images/MIKEOPERATIONS/export.png", "properties": {"configuration": "[Configuration]", "theme": "[Theme]", "OPTIONALObservationPeriod": "[ObservationPeriod]", "filterGroups": "[FilterGroups]", "featureType": "[FeatureType]", "apiUrl": "[ApiUrl]", "apiId": "[ApiId]", "documentConnection": "mo-doc"}}, {"displayTitle": "Job instances", "displaySubtitle": "Select a job instance to the left for inspection.", "url": "extensions/dhi-mo-jobs/dhi-mo-jobs.html", "elementName": "dhi-mo-jobs", "properties": {"apiUrl": "[ApiUrl]", "apiId": "[ApiIdNoObservationPeriodOffset]", "jobName": "[<PERSON><PERSON><PERSON>]", "settings": "[Settings]"}}], "OPTIONALdocuments": [{"url": "/dev/data/Samdus/SAMDUS_visualisering_Dokumentation_for_Superbrugere.pdf", "availableForConfigurationIds": ["Sava5"], "displayName": "<PERSON><PERSON><PERSON><PERSON>", "ribbonGroupName": "Dokumentation", "icon": "images/MIKEOPERATIONS/info24x24.png", "userRoles": ["Administrator"]}], "OPTIONALrasters": [{"filterGroups": ["GROUP1"], "themes": ["Styring - Skema", "Styring"], "type": "background", "url": "images/MIKEOPERATIONS/klimaspringSkema.png", "boundingBox": {"lowerLeft": {"latitude": 56.151024, "longitude": 10.209174}, "upperRight": {"latitude": 56.15504, "longitude": 10.219989}}, "center": {"latitude": 56.15300194058882, "longitude": 10.214425921440125, "zoom": 17}}, {"filterGroups": ["GROUP1"], "themes": ["Regn - Geografisk"], "type": "radar", "dataSource": {"host": "http://klimaspring.dhigroup.com", "radar": "aarhus-radar"}, "boundingBox": {"lowerLeft": {"latitude": 55.602311, "longitude": 9.036234225387542}, "upperRight": {"latitude": 56.664502, "longitude": 10.995200612874198}}, "center": {"latitude": 56.12565331475411, "longitude": 10.135574340820312, "zoom": 10}}]}