<!DOCTYPE html>
<html lang="en">
<head>
    <meta name="viewport"
		content="width=device-width, initial-scale=1.0" />
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <link href='https://fonts.googleapis.com/css?family=Roboto:400,100,300,700' rel='stylesheet' type='text/css'>
    
        <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
        
        <link rel="stylesheet" href="css/style.css">
<link rel="stylesheet"
		href=
"https://unpkg.com/purecss@2.0.6/build/pure-min.css">
   
    <title>Browse by datatype</title>
   
    <link rel="stylesheet" href=
"https://cdn.jsdelivr.net/npm/foundation-sites@6.7.4/dist/css/foundation.min.css"
		crossorigin="anonymous">

	<script src=
"https://cdnjs.cloudflare.com/ajax/libs/foundation/6.0.1/js/vendor/jquery.min.js">
	</script>

	<script src=
"https://cdn.jsdelivr.net/npm/foundation-sites@6.5.1/dist/js/foundation.min.js"
		crossorigin="anonymous">
        
	</script>
    <link rel="stylesheet" href="css/templatemo-style.css">
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="fontawesome/css/all.min.css">
    
    <style>
        
        .pure-menu-heading {
            background-color: #00e6ac;
            color: #eee;
            width: 100 px
        }      
            .custom-display {
            /* To limit the menu width to
                    the content of the menu: */
            display: inline-block;
            
			color: white;
            background-color: #eee;
            width: 150 px
            padding: 50 px
			text-decoration: none;
            }
            .pure-menu-item:hover {
                  background-color: #00e6ac;
                  width: 150 px
            }
            .lastone
            {
                background-color: #eee;
                width: 300 px
            }
            .tabs-panel is-active
            {
                background-color: #04AA6D;
                width: 100 px
            }

        #wrapper-div {
        background:#fff;
        display: flex;
        flex-wrap: wrap;
        }

        #wrapper-div>div {
            flex-grow: 1;
            width: auto;
            height: auto;
        }
        #year-table{
            border-collapse: collapse;
            border-left: 1px solid white;
        }
        #year-table tr:nth-child(even) {
            background-color: Lightgreen;
        }
        #year-table td{
            border: 1px solid green;
            padding: 10px;
            cursor: pointer;
        }
        #year-content-table{
            border-collapse: collapse;
            margin-top: 60px;
        }
        #year-content-table td, #year-content-table th{
            border: 1px solid black;
            padding: 10px;
        }
        #img-download-button{
            float: left;
            position: absolute;
            bottom: 0px;
        }
        #image-elem{
            display: inherit;
        }
    </style>



<!--
    
TemplateMo 556 Catalog-Z

https://templatemo.com/tm-556-catalog-z

-->
<link rel="stylesheet" href="css/bootstrap.min.css">
<link rel="stylesheet" href="fontawesome/css/all.min.css">
<link rel="stylesheet" href="css/templatemo-style.css">
</head>
<body>
    <!-- Page Loader -->
    <div id="loader-wrapper">
        <div id="loader"></div>

        <div class="loader-section section-left"></div>
        <div class="loader-section section-right"></div>

    </div>
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            
            <a class="navbar-brand" href="#">
              
                <img src="img/logo.png" alt="india logo" width="30" height="30" style="vertical-align:middle">
                 Integrated Water Resources Assessment of Udaipur, Rajasthan, India
            </a>
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                <i class="fas fa-bars"></i>
            </button>
            <div class="collapse navbar-collapse" id="navbarSupportedContent">
            <ul class="navbar-nav ml-auto mb-2 mb-lg-0">
                <li class="nav-item">
                    <a class="nav-link nav-link-1 active" aria-current="page" href="index.html">Data Catalog</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link nav-link-2" href="GIS Maps.html">GIS Maps</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link nav-link-3" href="about.html">Decision Support System</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link nav-link-4" href="contact.html">Mike SHE Model</a>
                </li>
            </ul>
            </div>
        </div>
    </nav>

    <div class="tm-hero d-flex justify-content-center align-items-center" data-parallax="scroll" data-image-src="img/hero.jpg">
        
    </div>
    <nav class="navbar navbar-expand-lg navbar-dark ftco_navbar bg-dark ftco-navbar-light" id="ftco-navbar">
	    <div class="container">
	    
	      <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#ftco-nav" aria-controls="ftco-nav" aria-expanded="false" aria-label="Toggle navigation">
	        <span class="fa fa-bars"></span> Menu
	      </button>
				<form action="#" class="searchform order-lg-last">
          <div class="form-group d-flex">
            <input type="text" class="form-control pl-3" placeholder="Search">
            <button type="submit" placeholder="" class="form-control search"><span class="fa fa-search"></span></button>
          </div>
        	</form>
	      <div class="collapse navbar-collapse" id="ftco-nav">
	        <ul class="navbar-nav mr-auto">
                <li class="nav-item active"><a href="#" class="nav-link">Home</a></li>
	        	<li class="nav-item dropdown">
              <a class="nav-link dropdown-toggle" href="#" id="dropdown04" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Data Catalog</a>
                <div class="dropdown-menu" aria-labelledby="dropdown04">
              	<a class="dropdown-item" href="#">View All Datasets</a>
                <a class="dropdown-item" href="#">Browse by data type</a>
                <a class="dropdown-item" href="#">Gis Data</a>
                
                </div> 
             </li>
          
	        	<li class="nav-item"><a href="#" class="nav-link">GIS Maps</a></li>
	        	<li class="nav-item"><a href="#" class="nav-link">Decision Support System</a></li>
	          <li class="nav-item"><a href="#" class="nav-link">Mike SHE Model</a></li>
	        </ul>
	      </div>
	    </div>
	 </nav>
    <div class="container-fluid tm-container-content tm-mt-60">
        <div class="row mb-4">
            <h2 class="col-12 tm-text-primary">SRTM Digital Elevation Data</h2>
            <p class="mb-4">
                The Shuttle Radar Topography Mission (SRTM) digital elevation dataset was originally produced to provide consistent, high-quality elevation data at near global scope. Spatial Resolution: 30 m.
            </p>
         </div>
        <div class="row tm-mb-90">            
            <div class="col-xl-8 col-lg-7 col-md-6 col-sm-12">
             <!--   <div id="wrapper-div"> -->
                    <div class=" pure-menu custom-display">
                        <ul class="pure-menu-list">
                        <li class="pure-menu-heading">
                            GIS DATA MENU
                        </li>
                    
                        <li class="pure-menu-item
                                    pure-menu-has-children
                                    pure-menu-allow-hover">
                            <a href="#" 
                            class="pure-menu-link">
                            Basic Data
                            </a>
                    
                            <ul class="pure-menu-children">
                            <li class="pure-menu-item">
                                <a href="#wrapper-div"
                                class="pure-menu-link">
                                Topography (DEM)
                                </a>
                            </li>
                            <li class="pure-menu-item">
                                <a href="#tab1"
                                class="pure-menu-link">
                                River Cross-Sectional Data
                                </a>
                            </li>
                            <li class="pure-menu-item">
                                <a href="#"
                                class="pure-menu-link">
                                Dam and Reservoir Details
                                </a>
                            </li>
                            <li class="pure-menu-item">
                                <a href="#"
                                class="pure-menu-link">
                                Geology - Lithology
                                </a>
                            </li>
                            <li class="pure-menu-item">
                                <a href="#"
                                class="pure-menu-link">
                                Surface Soil
                                </a>
                            </li>
                            <li class="pure-menu-item">
                                <a href="#"
                                class="pure-menu-link">
                                Udaipur MC and Population
                                </a>
                            </li>
                            </ul>
                    
                        </li>
                    
                        <li class="pure-menu-item
                                    pure-menu-has-children
                                    pure-menu-allow-hover">
                            <a href="#"
                            class="pure-menu-link">
                            Station based Climate Data
                            </a>
                    
                            <ul class="pure-menu-children">
                            <li class="pure-menu-item">
                                <a href="#"
                                class="pure-menu-link">
                                Precipitation (1973-2021)
                                </a>
                            </li>
                            <li class="pure-menu-item">
                                <a href="#"
                                class="pure-menu-link">
                                Temperature (2001-2021)
                                </a>
                            </li>
                            <li class="pure-menu-item">
                                <a href="#"
                                class="pure-menu-link">
                                Humidity (2001-2021)
                                </a>
                            </li>
                            <li class="pure-menu-item">
                                <a href="#"
                                class="pure-menu-link">
                                Wind speed (2001-2021)
                                </a>
                            </li>
                            <li class="pure-menu-item">
                                <a href="#"
                                class="pure-menu-link">
                                Evapotranspiration (2001)
                                </a>
                            </li>
                            </ul>
                        </li>
                    
                        <li class="pure-menu-item
                                    pure-menu-has-children
                                    pure-menu-allow-hover">
                            <a href="#"
                            class="pure-menu-link">
                            Hydrological Data
                            </a>
                            <ul class="pure-menu-children">
                            <li class="pure-menu-item">
                                <a href="#"
                                class="pure-menu-link">
                                Apply for Jobs
                                </a>
                            </li>
                            <li class="pure-menu-item">
                                <a href="#"
                                class="pure-menu-link">
                                Post a Jobs
                                </a>
                            </li>
                            </ul>
                        </li>
                        <li class="pure-menu-item
                        pure-menu-has-children
                        pure-menu-allow-hover">
                         <a href="#"
                        class="pure-menu-link">
                         Hydro-Geological Data
                        </a>
                       <ul class= "pure-menu-children">
                         <li class="pure-menu-item">
                          <a href="#"
                         class="pure-menu-link">
                             Apply for Jobs
                             </a>
                      </li>
                      <li class="pure-menu-item">
                       <a href="#"
                        class="pure-menu-link">
                     Post a Jobs
                      </a>
                      </li>
                      </ul>
                        </li>
                        <li class="pure-menu-item
                        pure-menu-has-children
                        pure-menu-allow-hover">
                        <a href="#"
                        class="pure-menu-link">
                        Water Quality Data
                        </a>
                        <ul class= "pure-menu-children">
                        <li class="pure-menu-item">
                            <a href="#"
                            class="pure-menu-link">
                            Apply for Jobs
                            </a>
                        </li>
                        <li class="pure-menu-item">
                            <a href="#"
                            class="pure-menu-link">
                            Post a Jobs
                            </a>
                        </li>
                    </ul>
                         </li>     
                            <li class="pure-menu-item
                                pure-menu-has-children
                                pure-menu-allow-hover">
                                <a href="#"
                                class="pure-menu-link">
                                Satellite based Data
                                </a>
                                <ul class= "pure-menu-children">
                                <li class="pure-menu-item">
                                    <a href="#"
                                    class="pure-menu-link">
                                    Apply for Jobs
                                    </a>
                                </li>
                                <li class="pure-menu-item">
                                    <a href="#"
                                    class="pure-menu-link">
                                    Post a Jobs
                                    </a>
                                </li>
                                </ul>
                            </li>  
                                            <li class="pure-menu-item">
                                                <a href="#"
                                                class="pure-menu-link">
                                                Satellite based Data
                                                </a>
                                            </li>
                                        
                                            </ul>
                    </div>
                   
                    
                </div>
                <div class="col-xl-4 col-lg-5 col-md-6 col-sm-12">
                    <div class="tabs-content vertical" data-tabs-content="vertical-tabs">
                        <div class="tabs-panel is-active" id="tab1">                    
                            <h2 class="col-12 tm-text-primary">SRTM Digital Elevation Data</h2>
                              <div class="text-center mb-5">
                                <p class="mbnew">
                                    The Shuttle Radar Topography Mission (SRTM) digital elevation dataset was originally produced to provide consistent, high-quality elevation data at near global scope. Spatial Resolution: 30 m
                                </p> 
                                <img src="img/Land Use and Land Cover.jpg" alt="india logo" width="420" height="420" style="vertical-align:middle">
                                <a href="#" class="btn btn-primary tm-btn-big">Download</a>
                             </div>   
                        </div>
        
                        <div class="tabs-panel" id="tab2">
                            <p>Tab 2 Image</p>
                            <strong>GeeksforGeeks Image</strong>
                            <img src=
                                         "https://media.geeksforgeeks.org/wp-content/cdn-uploads/20190710102234/download3.png"
                                alt="GFG">
                                <div class="text-center mb-5">
                            <a href="#" class="btn btn-primary tm-btn-big">Download</a>
                        </div>   
                        </div>
        
                        <div class="tabs-panel" id="tab3">
                            <p>Tab 3 content</p>
                            <p>
                                With the idea of imparting programming
                                knowledge, Mr. Sandeep Jain, an IIT Roorkee
                                alumnus started a dream, GeeksforGeeks.
                            </p>
                        </div>
        
                        <div class="tabs-panel" id="tab4">
                            <p>Tab 4 Image</p>
                            <strong>GFG Image</strong>
                            <img src=
                                          "https://media.geeksforgeeks.org/wp-content/cdn-uploads/gfg_200x200-min.png" alt="">
                        </div>
        
                        <div class="tabs-panel" id="tab5">
                            <p>Tab 5 content</p>
                            <p>
                                With every tick of time, we are adding
                                arrows in our quiver. From articles on various
                                computer science subjects for practice
                            </p>
                        </div>
                    </div>
                </div>
                 <!-- </div> -->
            </div>

          
           <!--   <div class="col-xl-4 col-lg-5 col-md-6 col-sm-12"> 
                <div id="wrapper-div"> 
                <div class="lastone">
                    <h2 class="col-12 tm-text-primary">SRTM Digital Elevation Data
                    </h2>
                    <p class="mbnew">
                        The Shuttle Radar Topography Mission (SRTM) digital elevation dataset was originally produced to provide consistent, high-quality elevation data at near global scope. Spatial Resolution: 30 m
                    </p>
                    <div>
                        <table id="year-content-table"></table>
                        <img src="img/LULC Legends.jpg" alt="india logo" width="420" height="420" style="vertical-align:middle">
                    </div>                                  
                    <div class="text-center mb-5">
                        <a href="#" class="btn btn-primary tm-btn-big">Download</a>
                    </div>                 
                    <div>
                        <h3 class="tm-text-gray-dark mb-3">Related Tags</h3>
                        <a href="#" class="tm-text-primary mr-4 mb-2 d-inline-block">Drainage Network</a>
                        <a href="#" class="tm-text-primary mr-4 mb-2 d-inline-block">Geomorphology</a>
                        <a href="#" class="tm-text-primary mr-4 mb-2 d-inline-block">NDVI Vegetation Indices</a>
                        <a href="#" class="tm-text-primary mr-4 mb-2 d-inline-block">Land Use and Land Cover</a>
                        <a href="#" class="tm-text-primary mr-4 mb-2 d-inline-block">Landforms</a>
                        <a href="#" class="tm-text-primary mr-4 mb-2 d-inline-block">GW DTWL Data</a>
                        <a href="#" class="tm-text-primary mr-4 mb-2 d-inline-block">Soil Texture Data</a>
                        <a href="#" class="tm-text-primary mr-4 mb-2 d-inline-block">SRTM Digital Elevation Model</a>
                    </div>
                </div>
             </div>
            </div>
        </div>
        <div class="row mb-4">
            <h2 class="col-12 tm-text-primary">
                Related images
            </h2>
        </div>
        <div class="row mb-3 tm-gallery">
            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12 mb-5">
                <figure class="effect-ming tm-video-item">
                    <img src="img/Drainage Network.jpg" alt="Image" class="img-fluid">
                    <figcaption class="d-flex align-items-center justify-content-center">
                        <h2>Drainage Network</h2>
                        <a href="#">View more</a>
                    </figcaption>                    
                </figure>
                <div class="d-flex justify-content-between tm-text-gray">
                    <span class="tm-text-gray-light">16 Oct 2020</span>
                    <span>Drainage Network</span>
                </div>
            </div>
            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12 mb-5">
                <figure class="effect-ming tm-video-item">
                    <img src="img/Geomorphology.jpg" alt="Image" class="img-fluid">
                    <figcaption class="d-flex align-items-center justify-content-center">
                        <h2>Perfumes</h2>
                        <a href="#">Geomorphology</a>
                    </figcaption>                    
                </figure>
                <div class="d-flex justify-content-between tm-text-gray">
                    <span class="tm-text-gray-light">12 Oct 2020</span>
                    <span>Geomorphology</span>
                </div>
            </div>
            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12 mb-5">
                <figure class="effect-ming tm-video-item">
                    <img src="img/NDVI Vegetation Indices.jpg" alt="Image" class="img-fluid">
                    <figcaption class="d-flex align-items-center justify-content-center">
                        <h2>NDVI Vegetation Indices</h2>
                        <a href="#">View more</a>
                    </figcaption>                    
                </figure>
                <div class="d-flex justify-content-between tm-text-gray">
                    <span class="tm-text-gray-light">8 Oct 2020</span>
                    <span>NDVI Vegetation Indices</span>
                </div>
            </div>
            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12 mb-5">
                <figure class="effect-ming tm-video-item">
                    <img src="img/Land Use and Land Cover.jpg" alt="Image" class="img-fluid">
                    <figcaption class="d-flex align-items-center justify-content-center">
                        <h2>Land Use and Land Cover</h2>
                        <a href="#">View more</a>
                    </figcaption>                    
                </figure>
                <div class="d-flex justify-content-between tm-text-gray">
                    <span class="tm-text-gray-light">6 Oct 2020</span>
                    <span>Land Use and Land Cover</span>
                </div>
            </div>
            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12 mb-5">
                <figure class="effect-ming tm-video-item">
                    <img src="img/Landforms.jpg" alt="Image" class="img-fluid">
                    <figcaption class="d-flex align-items-center justify-content-center">
                        <h2>Landforms</h2>
                        <a href="#">View more</a>
                    </figcaption>                    
                </figure>
                <div class="d-flex justify-content-between tm-text-gray">
                    <span class="tm-text-gray-light">26 Sep 2020</span>
                    <span>Landforms</span>
                </div>
            </div>
            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12 mb-5">
                <figure class="effect-ming tm-video-item">
                    <img src="img/GWDTWLData.jpg" alt="Image" class="img-fluid">
                    <figcaption class="d-flex align-items-center justify-content-center">
                        <h2>GW DTWL Data</h2>
                        <a href="#">View more</a>
                    </figcaption>                    
                </figure>
                <div class="d-flex justify-content-between tm-text-gray">
                    <span class="tm-text-gray-light">22 Sep 2020</span>
                    <span>GW DTWL Data</span>
                </div>
            </div>
            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12 mb-5">
                <figure class="effect-ming tm-video-item">
                    <img src="img/Soil Texture Data.jpg" alt="Image" class="img-fluid">
                    <figcaption class="d-flex align-items-center justify-content-center">
                        <h2>Soil Texture Data</h2>
                        <a href="#">View more</a>
                    </figcaption>                    
                </figure>
                <div class="d-flex justify-content-between tm-text-gray">
                    <span class="tm-text-gray-light">12 Sep 2020</span>
                    <span>Soil Texture Data</span>
                </div>
            </div>
            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12 mb-5">
                <figure class="effect-ming tm-video-item">
                    <img src="img/SRTM Digital Elevation Model.jpg" alt="Image" class="img-fluid">
                    <figcaption class="d-flex align-items-center justify-content-center">
                        <h2>SRTM Digital Elevation Model</h2>
                        <a href="#">View more</a>
                    </figcaption>                    
                </figure>
                <div class="d-flex justify-content-between tm-text-gray">
                    <span class="tm-text-gray-light">4 Sep 2020</span>
                    <span>SRTM Digital Elevation Model</span>
                </div>
            </div>        
        </div> <!-- row -->
    </div> <!-- container-fluid, tm-container-content --> 

    <footer class="tm-bg-gray pt-5 pb-3 tm-text-gray tm-footer">
        <div class="container-fluid tm-container-small">
            <div class="row">
                <div class="col-lg-6 col-md-12 col-12 px-5 mb-5">
                    <h3 class="tm-text-primary mb-4 tm-footer-title">About Udaipur</h3>
                    <p>Integer ipsum odio, pharetra ac massa ac, pretium facilisis nibh. Donec lobortis consectetur molestie. Nullam nec diam dolor. Fusce quis viverra nunc, sit amet varius sapien.</p>
                </div>
                <div class="col-lg-3 col-md-6 col-sm-6 col-12 px-5 mb-5">
                    <h3 class="tm-text-primary mb-4 tm-footer-title">Our Links</h3>
                    <ul class="tm-footer-links pl-0">
                        <li><a href="#">Advertise</a></li>
                        <li><a href="#">Support</a></li>
                        <li><a href="#">Our Company</a></li>
                        <li><a href="#">Contact</a></li>
                    </ul>
                </div>
                <div class="col-lg-3 col-md-6 col-sm-6 col-12 px-5 mb-5">
                    <ul class="tm-social-links d-flex justify-content-end pl-0 mb-5">
                        <li class="mb-2"><a href="https://facebook.com"><i class="fab fa-facebook"></i></a></li>
                        <li class="mb-2"><a href="https://twitter.com"><i class="fab fa-twitter"></i></a></li>
                        <li class="mb-2"><a href="https://instagram.com"><i class="fab fa-instagram"></i></a></li>
                        <li class="mb-2"><a href="https://pinterest.com"><i class="fab fa-pinterest"></i></a></li>
                    </ul>
                    <a href="#" class="tm-text-gray text-right d-block mb-2">Terms of Use</a>
                    <a href="#" class="tm-text-gray text-right d-block">Privacy Policy</a>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-8 col-md-7 col-12 px-5 mb-3">
                    Copyright 2020 Catalog-Z Company. All rights reserved.
                </div>
                <div class="col-lg-4 col-md-5 col-12 px-5 text-right">
                    Designed by <a href="https://templatemo.com" class="tm-text-gray" rel="sponsored" target="_parent">TemplateMo</a>
                </div>
            </div>
        </div>
    </footer>
    
    <script src="js/plugins.js"></script>
    <script src="js/jquery.min.js"></script>
  <script src="js/popper.js"></script>
  <script src="js/bootstrap.min.js"></script>
  <script src="js/main.js"></script>
    <script>
        $(window).on("load", function() {
            $('body').addClass('loaded');
        });
    </script>
    <script>
        document.body.onload=function(){
            var yearContents = [
                {
                    name: 2001,
                    imgSrc: "C:/Users/<USER>/OneDrive - DHI/Prabhu DHI work/DANIDA Project/templates/Backup-Template for DANIDA/templatemo_556_catalog_z/img/Land Use and Land Cover.jpg",
                    imgTitle: "Land Use Land Cover Layers",
                    
                },
                {
                    name: 2011,
                    imgSrc: "https://www.geoimage.com.au/wp-content/uploads/2021/09/Honolulu-Hawaii-Airbus.jpg",
                    imgTitle: "Land Use Land Cover Layers",
                    
                },
                {
                    name: 2021,
                    imgSrc: "https://eos.com/wp-content/uploads/2019/04/Main.jpg.webp",
                    imgTitle: "Land Use Land Cover Layers",
                    
                }
            ]
            function populateYearTable(yearContents, columns){
                var yearTable = document.getElementById("year-table")
                yearTable.querySelector("tbody").innerHTML = ""
                
                var totalYears = yearContents.length
                for(var i=0; i<Math.ceil(totalYears/columns);i++){
                    var tr = document.createElement("tr")
                    for(var j=0; j<columns; j++){
                        var k = (i*columns)+j
                        var currentYear = (k >= totalYears)? "": yearContents[k]["name"]
                        var td = document.createElement("td")
                        td.onclick = displayContentTable.bind(true, yearContents[k])
                        td.innerText = currentYear
                        tr.append(td)
                    }
                    yearTable.querySelector("tbody").append(tr)
                }
                if(totalYears>0){
                    displayContentTable(yearContents[0])
                }
            }

            function downloadImage(e){
                var a = document.createElement('a');
                a.href = document.getElementById("image-elem").getAttribute("src");
                a.download = document.getElementById("image-title").innerText+".jpg";
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
            }

            function displayContentTable(yearContent){
                var imgSrc = yearContent["imgSrc"] 
                var imgTitle = yearContent["imgTitle"]
                var contents = yearContent["contents"]
                var imgTitleElem = document.getElementById("image-title")
                imgTitleElem.innerText = imgTitle
                var imageElem = document.getElementById("image-elem")
                imageElem.setAttribute("alt",  "Loading")
                imageElem.setAttribute("src", "")
                imageElem.setAttribute("src", imgSrc)

                var table = document.getElementById("year-content-table")
                table.innerHTML=""
                var headers = contents["header"].map((x)=>{return `<th>${x}</th>`})
                var body = ""
                contents["body"].forEach(e => {
                    body+="<tr>"+e.map((x)=>{return `<td>${x}</td>`}).join("")+"</tr>"
                });
                table.innerHTML = "<thead><tr>"+headers.join("")+"</tr></thead><tbody>"+body+"</tbody>"
            }

            populateYearTable(yearContents, 5)
            document.getElementById("img-download-button").onclick = downloadImage
        }

      </script>
</body>
</html>