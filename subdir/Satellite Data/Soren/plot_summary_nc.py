# -*- coding: utf-8 -*-
"""
Created on Wed Jun 30 15:17:50 2021

@author: wqg436
"""
import netCDF4 as nc
import pandas as pd
import os
import datetime
from dateutil.relativedelta import relativedelta
import matplotlib.pyplot as plt
import numpy as np
import matplotlib as mpl
import geopandas as gp
from matplotlib.collections import LineCollection

# %% Observations
st_path = '../4.Climate Data/Rainfall Gauge Stations June 2021/'
combined = pd.read_csv(st_path + 'final/rain_gauge_st_ts_2000-2021.csv',
                       index_col='Date')
combined.index = pd.to_datetime(combined.index)

# calculate
mon = combined.resample('M').agg(pd.Series.sum, min_count=2).mean(axis=1)
ann_obs = combined.resample('Y').agg(pd.Series.sum, min_count=2).mean(axis=1)
mon_obs = [float(mon.loc[mon.index.month == month].mean())
           for month in range(1, 13)]
# %% Loading IMD data
fn = '../4.Climate Data//IMD_Rainfall_2000-2020_Bigod.nc'
ds = nc.Dataset(fn)

# extract keys
dimensions = list(ds.dimensions.keys())
variables = list(ds.variables.keys())

data_key = [var for var in variables if var not in dimensions]
lon_key = [dim for dim in dimensions if 'lon' in dim.lower()]
lat_key = [dim for dim in dimensions if 'lat' in dim.lower()]
time_key = [dim for dim in dimensions if 'time' in dim.lower()]

# extract values and correct for area
data = ds.variables['pre'][:]
data[data > 1000] = np.nan

# Calculate dates
t_unit = ds[time_key[0]].units
delta_unit = t_unit.split()[0]
date = np.array(t_unit.split()[2:][0].split('-'),
                dtype=int)
st_date = datetime.datetime(date[0], date[1], date[2])
t_ori = ds.variables[time_key[0]][:]
time = [st_date + relativedelta(**{delta_unit: t_ori[t]})
        for t in range(t_ori.shape[0])]
ds.close()

# loading area
data_type = 'IMD'
area = np.load('./area_ws_in_grid_'+data_type+'.npy')
area = area/np.max(area)  # percentage of cell

data1 = data*area[None, :, :]
data1 = data1.sum(axis=1).sum(axis=1)/np.sum(area)

# Summary statistic for array
df_imd = pd.DataFrame(index=time)
for t in time:
    try:
        df_imd.loc[t, 'mean'] = data1[time.index(t)]
    except ValueError:  # no values in array
        df_imd.loc[t, 'mean'] = np.nan

# calculate
mon = df_imd.resample('M').agg(pd.Series.sum, min_count=2).mean(axis=1)
ann_imd = df_imd.resample('Y').agg(pd.Series.sum, min_count=2).mean(axis=1)
mon_imd = [float(mon.loc[mon.index.month == month].mean())
           for month in range(1, 13)]

# %% Loading Satellite products
data_type = 'P'

path = './clipped/'+data_type+'/'
files = os.listdir(path)
dfs = [None]*len(files)

area = np.load('./area_ws_in_grid_'+data_type+'.npy')
area = area/np.max(area)  # percentage of cell

for i in range(len(files)):
    # opening file
    fn = path + files[i]
    ds = nc.Dataset(fn)

    # extract keys
    dimensions = list(ds.dimensions.keys())
    variables = list(ds.variables.keys())

    data_key = [var for var in variables if var not in dimensions]
    lon_key = [dim for dim in dimensions if 'lon' in dim.lower()]
    lat_key = [dim for dim in dimensions if 'lat' in dim.lower()]
    time_key = [dim for dim in dimensions if 'time' in dim.lower()]

    # extract values and correct for area of watershed in raster
    data = ds.variables[data_key[2]][:]
    data1 = data*area[None, :, :]
    data1 = data1.sum(axis=1).sum(axis=1)/np.sum(area)

    # Calculate dates
    t_unit = ds[time_key[0]].units
    delta_unit = t_unit.split()[0]
    date = np.array(t_unit.split()[2:][0].split('-'),
                    dtype=int)
    st_date = datetime.datetime(date[0], date[1], date[2])
    t_ori = ds.variables[time_key[0]][:]
    time = [st_date + relativedelta(**{delta_unit: t_ori[t]})
            for t in range(t_ori.shape[0])]

    # Summary statistic for array
    df = pd.DataFrame(index=time)
    for t in time:
        try:
            df.loc[t, 'mean'] = data1[time.index(t)]
        except ValueError:  # no values in array
            df.loc[t, 'mean'] = np.nan
    dfs[i] = df

# resample to monthly values for P to match the unit of ET
if data_type == 'P':
    for i in range(len(files)):
        dfs[i] = dfs[i].resample("M").agg(pd.Series.sum, min_count=2)

# %% Plotting as Sorens plot
# Setting style
plt.style.use('ggplot')
cycle = [p['color'] for p in plt.rcParams['axes.prop_cycle']]
cycle += ["k", "c"]
mpl.rcParams['axes.prop_cycle'] = mpl.cycler(color=cycle)

fig, ax = plt.subplots(1, 2, figsize=(10, 3.5))
full_t = 365 if data_type == 'P' else 12
for i in range(len(files)):
    label = files[i].replace('ET_', '').replace('PREC_', '').replace('.nc', '')
    year_min = dfs[i].index.year.min()
    year_max = dfs[i].index.year.max()
    year_max = year_max if np.sum(dfs[i].index.year == year_max) > full_t/2 \
        else year_max - 1
    ann = dfs[i].resample('Y').sum()
    ann[ann == 0] = np.nan
    mon = [float(dfs[i].loc[dfs[i].index.month == month].mean())
           for month in range(1, 13)]
    ax[0].plot(np.arange(dfs[i].index.month.min(),
                         dfs[i].index.month.max()+1), mon)
    ax[1].plot(ann.index.values, ann,
               label=label)

# ylabels
ax[0].set_ylabel(data_type + ' [mm/month]')
ax[1].set_ylabel(data_type + ' [mm/year]')

# # legend
plt.legend(bbox_to_anchor=(1.05, 1), ncol=1)

# # xticks
months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
          'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
year_min = np.min([dfs[i].index.year.min() for i in range(len(files))])
year_max = np.max([dfs[i].index.year.max() for i in range(len(files))])
years = np.arange(year_min, year_max, 2)
ax[0].set_xticks(ticks=np.arange(1, 13))
ax[0].set_xticklabels(months, rotation=90)
plt.xticks(rotation=90)

# titles
ax[0].set_title('Monthly ' + data_type)
ax[1].set_title('Annual ' + data_type)

# save figure
plt.tight_layout()
plt.savefig('./Figures/ws_'+data_type, dpi=300, bbox_inches='tight')

# %% Plotting with obs only P
# Setting style
plt.style.use('ggplot')
cycle = [p['color'] for p in plt.rcParams['axes.prop_cycle']]
cycle += ["k", "c"]
mpl.rcParams['axes.prop_cycle'] = mpl.cycler(color=cycle)
if data_type == 'P':
    fig, ax = plt.subplots(1, 2, figsize=(10, 3.5))
    full_t = 365 if data_type == 'P' else 12
    for i in range(len(files)):
        label = files[i].replace('ET_', '').replace('PREC_', '').replace('.nc', '')
        year_min = dfs[i].index.year.min()
        year_max = dfs[i].index.year.max()
        year_max = year_max if np.sum(dfs[i].index.year == year_max) > full_t/2 \
            else year_max - 1
        ann = dfs[i].resample('Y').sum()
        ann[ann == 0] = np.nan
        mon = [float(dfs[i].loc[dfs[i].index.month == month].mean())
               for month in range(1, 13)]
        ax[0].plot(np.arange(dfs[i].index.month.min(),
                             dfs[i].index.month.max()+1), mon)
        ax[1].plot(ann.index.values, ann,
                   label=label)
    # obs
    ax[0].plot(np.arange(dfs[i].index.month.min(),
                         dfs[i].index.month.max()+1), mon_obs, linestyle='--')
    ax[1].plot(ann_obs.index.values, ann_obs,
               label='Obs', linestyle='--')

    # IMD data
    ax[0].plot(np.arange(dfs[i].index.month.min(),
                         dfs[i].index.month.max()+1), mon_imd, linestyle='--')
    ax[1].plot(ann_imd.index.values, ann_imd,
               label='IMD', linestyle='--')
    # ylabels
    ax[0].set_ylabel(data_type + ' [mm/month]')
    ax[1].set_ylabel(data_type + ' [mm/year]')

    # # legend
    plt.legend(bbox_to_anchor=(1.05, 1), ncol=1)

    # # xticks
    months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
              'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
    year_min = np.min([dfs[i].index.year.min() for i in range(len(files))])
    year_max = np.max([dfs[i].index.year.max() for i in range(len(files))])
    years = np.arange(year_min, year_max, 2)
    ax[0].set_xticks(ticks=np.arange(1, 13))
    ax[0].set_xticklabels(months, rotation=90)
    plt.xticks(rotation=90)

    # titles
    ax[0].set_title('Monthly ' + data_type)
    ax[1].set_title('Annual ' + data_type)

    # save figure
    plt.tight_layout()
    plt.savefig('./Figures/ws_'+data_type+'_obs', dpi=300, bbox_inches='tight')

# %% 2D plan view maps - Loading
data_type = 'P'

ws = gp.read_file('../3.Basic System Data/01 Shapefiles/WatershedAtBigod.shp')

long_name = 'Precipitation' if data_type == 'P' else 'Evapotranspiration'
var = 'pre' if data_type == 'P' else 'et'

nc_path = './clipped/'+data_type+'/'
nc_files = os.listdir(nc_path)
nc_files = [nc_path+'/'+file for file in nc_files]
if data_type == 'P':
    nc_files.append('../4.Climate Data/IMD_Rainfall_2000-2020_Bigod.nc')

mean_anns = [None]*len(nc_files)
lats = [None]*len(nc_files)
lons = [None]*len(nc_files)
for n in range(len(nc_files)):
    # opening file
    ds = nc.Dataset(nc_files[n])
    data = ds.variables[var][:]

    # extract keys
    lats[n] = ds.variables['lat'][:]
    lons[n] = ds.variables['lon'][:]

    # loading area
    data_type = 'IMD' if 'IMD' in nc_files[n] else data_type
    area = np.load('./area_ws_in_grid_'+data_type+'.npy')
    area = area/np.max(area)  # percentage of cell

    # Calculate dates
    t_unit = ds['time'].units
    delta_unit = t_unit.split()[0]
    date = np.array(t_unit.split()[2:][0].split('-'),
                    dtype=int)
    st_date = datetime.datetime(date[0], date[1], date[2])
    t_ori = ds.variables['time']
    time = [st_date + relativedelta(**{delta_unit: t_ori[t]})
            for t in range(t_ori.shape[0])]

    # Calculate mean annual prec
    years = [i for i in range(2000, 2021)]
    ann = np.zeros((len(years), data.shape[1], data.shape[2]))
    for i, year in enumerate(years):
        idx = [i for i, t in enumerate(time) if t.year == year]
        ann[i, :, :] = np.nansum(data[idx, :, :], axis=0).data
    ann[ann == 0] = np.nan
    mean_ann = np.nanmean(ann, axis=0)
    mean_ann[area == 0] = np.nan
    mean_anns[n] = mean_ann
data_type = 'P' if data_type == 'IMD' else data_type
ds.close()

# %% 2D plan view maps - Plotting
fig, ax = plt.subplots(3, 3, figsize=(7, 5))
plt.style.use('default')
kwargs1 = {'color': 'k', 'lw': 0.1}
ax = ax.ravel()
vmin = 500 if data_type == 'P' else 300
vmax = 900 if data_type == 'P' else 700
for n in range(len(nc_files)):
    ax[n].set_xlim(73.25, 75.3)
    ax[n].set_ylim(24.25, 25.45)
    ax[n].axis('off')
    ax[n].set_aspect('equal')
    ws.plot(ax=ax[n], facecolor='none', edgecolor='green', lw=0.5, zorder=100)
    segs1 = np.stack((lons[n], lats[n]), axis=2)
    segs1 = np.ma.masked_where(segs1 > 1000, segs1.data)
    segs2 = segs1.transpose(1, 0, 2)
    ax[n].add_collection(LineCollection(segs1, **kwargs1))
    ax[n].add_collection(LineCollection(segs2, **kwargs1))
    if 'IMD' not in nc_files[n]:  # Satellite products
        title = nc_files[n].replace('PREC_', '').replace('ET_', '').replace('.nc', '').replace(nc_path, '').replace('/', '')
        ax[n].set_title(title)
        # Satelite data
        h = ax[n].pcolormesh(lons[n], lats[n], mean_anns[n], vmin=vmin,
                             vmax=vmax, cmap='inferno')
    else:  # IMD
        ax[n].pcolormesh(lons[n], lats[n], mean_anns[n], vmin=vmin, vmax=vmax,
                         cmap='inferno')
        ax[n].set_title('IMD')
cbaxes = fig.add_axes([0.3, 0.05, 0.45, 0.03])
cb = plt.colorbar(h, label=long_name+' [mm/year]', orientation='horizontal',
                  cax=cbaxes)

for n in range(n, len(ax)):
    ax[n].axis('off')
fig.suptitle('Mean Annual '+data_type, weight='bold')
plt.savefig('./Figures/Map_view_annual_'+data_type+'.png', dpi=300,
            bbox_inches='tight')
