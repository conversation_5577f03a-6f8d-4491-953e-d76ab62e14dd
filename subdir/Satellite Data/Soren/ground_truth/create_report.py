# -*- coding: utf-8 -*-
"""
Created on Tue Aug 10 10:45:31 2021

@author: wqg436
"""

import docx
from docx.shared import Inches, Pt, RGBColor
import pandas as pd

periods = ['Day', 'Month', 'Year']
per_adverbs = ['Daily', 'Monthly', 'Annual']

xl = pd.ExcelFile('./Contingency_table.xlsx')
st_labels = xl.sheet_names
df = pd.DataFrame(columns=['Satellite Products']+st_labels)
events = ['P=0', 'Heavy Rainfall']
event_descr = ['days without rain (p = 0 mm/day)',
               'heavy rainfall events (90th percentile of non-zero gauge observations)']

# Create an instance of a word document
doc = docx.Document()

# styles
style = doc.styles['Caption']
font = style.font
font.name = 'Calibri'
font.size = Pt(10)
font.color.rgb = RGBColor.from_string('000000')  # black
font.italic = True
font.bold = False

style = doc.styles['Heading 1']
font = style.font
font.name = 'Calibri'
font.size = Pt(12)
font.color.rgb = RGBColor.from_string('000000')  # black
font.italic = False
font.bold = True

style = doc.styles['Normal']
font = style.font
font.name = 'Calibri'
font.size = Pt(9)
font.color.rgb = RGBColor.from_string('000000')  # black

# Initialize figure and table numbers
fign = 0
tabn = 0

# Add a Title to the document
doc.add_heading('Point to pixel evaluation', 0)

# Intro figure
fign += 1
doc.add_picture('../../4.Climate Data\Rainfall Gauge Stations June 2021/Overview_rainfall_stations_filt.png')
doc.add_paragraph('Figure '+str(fign)+': Spatial distribution of rain gauge' +
                  ' stations and the grid of the satellite products.',
                  style='Caption')

# results sections for all periods
for per in periods:
    adv = per_adverbs[periods.index(per)]
    doc.add_heading(adv+' scale', 1)

    fign += 1
    doc.add_picture('Stats_'+per+'.png', width=Inches(8.27*0.7),
                    height=Inches(11.69*0.7))
    cap = doc.add_paragraph('', style='Caption')
    cap.add_run('Figure '+str(fign)+': ').bold = True
    cap.add_run(adv+' rainfall at gauge' +
                ' stations (columns) compared to rainfall in ' +
                'correpsonding cell in satellite ' +
                'products (rows). The statistical values bias, MAE, ' +
                'and RMSE values are in millimeters. The 1:1 ratio is ' +
                'represented by a dashed, black line. ')

    fign += 1
    st_label = 'Bagolia'
    doc.add_picture('Error_'+per+'_'+st_label+'.png', width=Inches(8.27*0.7),
                    height=Inches(11.69*0.7))
    cap = doc.add_paragraph('', style='Caption')
    cap.add_run('Figure '+str(fign)+': ').bold = True
    cap.add_run(adv+' error of satellite products compared to observed ' +
                'rainfall at gauge station '+st_label+'. Positive values ' +
                'indicate that the satellite product overestimates the ' +
                'observed rainfall. ')
    if per != 'Year':
        cap.add_run('Light grey background highlights the rainy period from ' +
                    'June to October.')
    # Pierce skill score
    if per == 'Day':
        for event in events:

            # loading data
            for st_label in st_labels:
                sheet = pd.read_excel('./Contingency_table.xlsx',
                                      sheet_name=st_label)

                df[st_label] = sheet[event]
            df['Satellite Products'] = sheet['Satellite Products']

            # add a table to the end and create a reference variable
            # extra row is so we can add the header row
            tabn += 1
            cap = doc.add_paragraph('', style='Caption')
            cap.add_run('Table '+str(tabn)+': ').bold = True
            cap.add_run('Peirce Skill Score of ' +
                        event_descr[events.index(event)] +
                        ' at the different rain gauge stations for the' +
                        ' different satellite products.')
            t = doc.add_table(df.shape[0]+1, df.shape[1])
            t.style = 'Table Grid'
            # add the header rows.
            for j in range(df.shape[-1]):
                t.cell(0, j).text = df.columns[j]

            # add the rest of the data frame
            for i in range(df.shape[0]):
                for j in range(df.shape[-1]):
                    val = df.values[i, j]
                    val = '{:0.2f}'.format(val) if type(val) is float else val
                    t.cell(i+1, j).text = val

# Save document
doc.save('report_.docx')
