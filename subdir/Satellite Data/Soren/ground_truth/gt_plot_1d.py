# -*- coding: utf-8 -*-
"""
Created on Tue Aug 10 15:11:26 2021

@author: wqg436
"""
import pandas as pd
import os
import numpy as np
import matplotlib.pyplot as plt
from pandas import ExcelWriter
import matplotlib.dates as mdates
import seaborn as sns

st_labels = ['Bagolia*', 'Mavli', 'Nathdwara', 'Vallabh nagar', 'Bhopalsagar*']
nc_files = os.listdir('../original/P')
products = [n.replace('PREC_', '').replace('.nc', '') for n in nc_files]
periods = ['Day', 'Month', 'Year']
per = 'Day'  # period
p, n = 0, 0
st = st_labels[n]
prod = products[p]
# %% Loading data for plotting
station_ts = [None] * len(st_labels)
for n, st in enumerate(st_labels):
    station_ts[n] = pd.read_csv('./point_data/P_'+st.replace('*', '')+'.csv',
                                index_col=0)
    station_ts[n].index = pd.to_datetime(station_ts[n].index)

# %% Plotting annual station specific data
plt.style.use('ggplot')
# years = np.arange(2000, 2021, 2)
# import matplotlib.ticker as ticker
for n, st in enumerate(st_labels):
    ts = station_ts[n]
    annual = ts.resample("Y").sum()
    annual = annual.replace(0, np.nan)

    # plotting
    fig, ax = plt.subplots()
    annual.iloc[:, :-1].plot(ax=ax)
    annual['Obs'].plot(linestyle='--', color='k', ax=ax)
    ax.set_title('Rain gauge station: ' + st)
    plt.legend(bbox_to_anchor=(1.05, 1), ncol=1)
    ax.set_ylabel('Precipitation [mm/year]')
    plt.gca().xaxis.set_major_locator(plt.MultipleLocator(1))
    labels = ax.get_xticks()
    # ax.xaxis.set_major_locator(ticker.MultipleLocator(2))
    # # ax.set_xticks(ticks=years)
    # ax.set_xticklabels(years, rotation=90)

# %% Statitics plots + table
stats = ['r', 'Bias', 'MAE', 'RMSE']
per = 'Day'  # period
stats_tab = pd.DataFrame(columns=stats, index=products)
plt.style.use('default')

for per in periods:
    fig, ax = plt.subplots(len(products), len(st_labels),
                           figsize=(8.27*1.5, 11.69*1.5), sharey=True,
                           sharex=True)
    for p, prod in enumerate(products):
        for n, st in enumerate(st_labels):
            # fig, ax = plt.subplots()
            ts = station_ts[n]

            # get data
            resampled = ts.resample(per[0]).sum()
            # resampled = resampled.replace(0, np.nan)
            res = resampled[[prod, 'Obs']].dropna()

            # calculation of statistics
            stats_tab.loc[prod, 'r'] = np.corrcoef(res['Obs'], res[prod])[0, 1]
            stats_tab.loc[prod, 'Bias'] = np.mean(res[prod]) - np.mean(res['Obs'])
            stats_tab.loc[prod, 'MAE'] = np.mean(np.abs(res[prod] - res['Obs']))
            se = (res['Obs'] - res[prod])**2
            mse = np.mean(se)
            stats_tab.loc[prod, 'RMSE'] = np.sqrt(mse)

            text = 'r = {:0.2f}\n'.format(stats_tab.loc[prod, 'r']) +\
                   'Bias = {:0.2f}\n'.format(stats_tab.loc[prod, 'Bias']) +\
                   'MAE = {:0.2f}\n'.format(stats_tab.loc[prod, 'MAE']) +\
                   'RMSE = {:0.2f}'.format(stats_tab.loc[prod, 'RMSE'])

            # plotting
            ax[p, n].annotate(text, xy=(0.05, 0.95), xycoords='axes fraction',
                              va='top')
            ax[p, n].scatter(res[prod], res['Obs'], marker='x', color='grey',
                             s=5)

            mini = res.min().min()
            maxi = res.max().max()
            ax[p, n].plot([mini, maxi], [mini, maxi], color='k', linestyle='--')
            ax[p, n].set_xlim(mini, maxi)
            ax[p, n].set_ylim(mini, maxi)
            ax[p, n].set_aspect('equal')
            if p == len(products)-1:
                ax[p, n].set_xlabel('Rain gauge [mm/'+per.lower()+']')
            if n == 0:
                ax[p, n].set_ylabel(prod + ' [mm/'+per.lower()+']')
            if p == 0:
                ax[p, n].set_title(st_labels[n])
        plt.savefig('./figures/Stats_'+per+'.png',
                    dpi=300, bbox_inches='tight')
# %%


def error_plot(res, per, ax):
    marker = 'None' if per == 'Day' else 'None' if per == 'Month' else 'o'
    # s = .5 if per == 'Day' else 1 if per == 'Month' else 5
    s = 5
    ax.plot(res.index.values, res['error'], color='k', marker=marker,
            markersize=s)
    if per != 'Year':
        xrange = [res.index.min().year, res.index.max().year]
        # xrange = [mdates.num2date(xlim).year for xlim in ax.get_xlim()]
        for year in range(xrange[0], xrange[1]+1):
            ax.axvspan(*mdates.datestr2num(['6/1/{:d}'.format(year),
                                            '10/1/{:d}'.format(year)]),
                       color='gainsboro')
    ax.axhline(y=0, color='k', linestyle='-', lw=1)
    if p == len(products)-1:
        ax.set_xlabel('Period ['+per.lower()+'s]')
    ax.set_ylabel('Error [mm/'+per.lower()+']')
    ax.set_title(prod)
    ylim = (-200, 200) if per != 'Year' else (-600, 600)
    ax.set_ylim(ylim)


def error_plot_bar(res, per, ax):
    res = res.replace(0, np.nan).dropna(subset=['error'])

    bw = 300 if per == 'Year' else 30 if per == 'Month' else 1  # bar width
    # plotting
    bars = ax[p].bar(x=res.index.values, height=res['error'],
                     color='r', snap=False)
    for bar in bars:
        bar.set_width(bw)

    # ax.plot(res.index.values, res['error'], color='k', lw=0.5)
    ax[p].axhline(y=0, color='k', linestyle='-', lw=0.1)
    if p == len(products)-1:
        ax[p].set_xlabel('Period ['+per.lower()+'s]')
    ax[p].set_ylabel('Error [mm/'+per.lower()+']')
    ax[p].set_title(prod)


per = 'Year'  # period
fig, ax = plt.subplots()
ts = station_ts[n]

# get data
res = ts[[prod, 'Obs']].dropna()
res = res.resample(per[0]).sum()
res['error'] = res[prod] - res['Obs']
error_plot(res, per, ax)

# %% Error plots
plt.style.use('default')

for per in periods:
    for n, st in enumerate(st_labels):
        fig, ax = plt.subplots(len(products), 1, figsize=(8.27*1.5, 11.69*1.5),
                               sharey=True, sharex=True)
        for p, prod in enumerate(products):
            ts = station_ts[n]

            # get data
            res = ts[[prod, 'Obs']].dropna()
            res = res.resample(per[0]).sum()
            res['error'] = res[prod] - res['Obs']

            error_plot(res, per, ax[p])
        plt.tight_layout()
        lab = st_labels[n].replace('*', '')
        plt.savefig('./figures/Error_'+per+'_'+lab+'.png',
                    dpi=300, bbox_inches='tight')

# %% Contingency table
# cont table columns = Rain gauge, rows = sat product
cont_0 = pd.DataFrame(index=['Yes', 'No'], columns=['Yes', 'No'])
cont_90 = pd.DataFrame(index=['Yes', 'No'], columns=['Yes', 'No'])
conts = [cont_0, cont_90]
p90 = ts.loc[ts['Obs'] != 0, 'Obs'].quantile(0.9)  # 90th percentile
cols = ['P=0', 'Heavy Rainfall']
pss_table = pd.DataFrame(index=products, columns=cols)
w = ExcelWriter('./figures/Contingency_table.xlsx')
for n, st in enumerate(st_labels):
    for p, prod in enumerate(products):
        ts = station_ts[n]
        # drop nan values
        ts = ts[[prod, 'Obs']].dropna()

        # Calculate contingency tables
        cont_0.loc['Yes', 'Yes'] = np.sum((ts[prod] == 0) & (ts['Obs'] == 0))  # Hits
        cont_0.loc['Yes', 'No'] = np.sum((ts[prod] == 0) & (ts['Obs'] != 0))  # False Alarm
        cont_0.loc['No', 'No'] = np.sum((ts[prod] != 0) & (ts['Obs'] != 0))  # Correct Negatives
        cont_0.loc['No', 'Yes'] = np.sum((ts[prod] != 0) & (ts['Obs'] == 0))  # Misses

        cont_90.loc['Yes', 'Yes'] = np.sum((ts[prod] >= p90) & (ts['Obs'] >= p90))  # Hits
        cont_90.loc['Yes', 'No'] = np.sum((ts[prod] >= p90) & (ts['Obs'] < p90))  # False Alarm
        cont_90.loc['No', 'No'] = np.sum((ts[prod] < p90) & (ts['Obs'] < p90))  # Correct Negatives
        cont_90.loc['No', 'Yes'] = np.sum((ts[prod] < p90) & (ts['Obs'] >= p90))  # Misses

        for i, cont in enumerate(conts):
            POD = cont.loc['Yes', 'Yes']/(cont.loc['Yes', 'Yes'] + cont.loc['No', 'Yes'])
            POFD = cont.loc['Yes', 'No']/(cont.loc['Yes', 'No'] + cont.loc['No', 'No'])
            PSS = POD - POFD
            pss_table.loc[products[p], cols[i]] = PSS
    lab = st_labels[n].replace('*', '')
    pss_table.index.name = 'Satellite Products'
    pss_table.to_excel(w, sheet_name=lab)
w.save()
# %%

rank = pd.DataFrame(index=products, columns=cols)
for i, cont in enumerate(conts):
    t = pss_table.sort_values(by=cols[i], ascending=False).index.to_list()
    rank.loc[t, cols[i]] = np.arange(1, len(products)+1)

# %% Boxplots of stats distribution for all stations
fig, ax = plt.subplots(len(periods), len(stats), figsize=(10, 10), sharey=True)
for p, per in enumerate(periods):
    ax[p, 0].set_ylabel(per, size='large', weight='bold')
    for s, stat in enumerate(stats):
        df = pd.read_excel('./point_data/Stats_'+per+'.xlsx', sheet_name=stat,
                           index_col=0)

        sns.boxplot(data=df.T, orient="h", ax=ax[p, s])
        if p == len(periods)-1:
            ax[p, s].set_xlabel(stat, size='large', weight='bold')