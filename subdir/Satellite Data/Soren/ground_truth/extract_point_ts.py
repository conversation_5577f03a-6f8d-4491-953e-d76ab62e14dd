# -*- coding: utf-8 -*-
"""
Created on Tue Aug  3 13:40:08 2021

@author: wqg436
"""
import pandas as pd
import os
import netCDF4 as nc
import numpy as np
import datetime
from dateutil.relativedelta import relativedelta


def extract_ts(nc_fn, df):
    '''
    Extracts time series at pixel of nc file containing points in dataframe.
    Parameters
    ----------
    nc_fn : String
        nc file from which to extract pixel time series
    df : dataframe
        dataframe containing points in space at which to extract time series.

    Returns
    -------
    tss : list
        list of length of df points containing time series of nc file
        product.
    '''

    # loading nc-file
    ds = nc.Dataset(nc_fn)

    # extracting gridded geographic information of nc-file
    lat = ds.variables['lat'][:]
    lon = ds.variables['lon'][:]

    # Calculate dates
    t_unit = ds['time'].units
    delta_unit = t_unit.split()[0]
    date = np.array(t_unit.split()[2:][0].split('-'),
                    dtype=int)
    st_date = datetime.datetime(date[0], date[1], date[2])
    t_ori = ds.variables['time'][:].data
    time = [st_date + relativedelta(**{delta_unit: int(t)})
            for t in t_ori]

    # loop throug stations
    tss = [None]*df.shape[0]
    for key, df_row in df.iterrows():
        point = df_row[['Lat', 'Lon']].to_list()

        # finding closest row, col in raster of point
        distance = (lat-point[0])**2 + (lon-point[1])**2
        row, col = np.where(distance == distance.min())

        # extracting station specific data
        array = ds['pre'][:].data
        ts_ = array[:, row, col]
        ts_[ts_ > 1000] = 0

        # add dataframe to list
        tss[key] = pd.DataFrame(data=ts_, index=time)
    return tss


# %% Load obs data
st_path = '../../4.Climate Data/Rainfall Gauge Stations June 2021/'
combined = pd.read_csv(st_path + 'final/rain_gauge_st_ts_2000-2021.csv',
                       index_col='Date')
combined.index = pd.to_datetime(combined.index)

stations = pd.read_csv(st_path + 'final/rain_gauge_info.csv')
st_labels = stations.Station.to_list()

# %% Extracting time series and saving
nc_path = '../clipped/P'
nc_files = os.listdir(nc_path)
products = [n.replace('PREC_', '').replace('.nc', '') for n in nc_files]
nc_files = [nc_path + '/' + file for file in nc_files]

# adding IMD
nc_files.append('../../4.Climate Data//IMD_Rainfall_2000-2020_Bigod.nc')
products.append('IMD')

# extrating time series
station_ts = [pd.DataFrame(columns=products,
                           index=pd.date_range(start='1/1/2000',
                                               end='31/12/2020'))
              for i in range(len(st_labels))]

for i in range(len(nc_files)):
    print(products[i])
    tss = extract_ts(nc_files[i], stations)
    for n in range(stations.shape[0]):
        station_ts[n][products[i]] = tss[n]
        if i == len(nc_files)-1:
            ts = station_ts[n]
            ts['Obs'] = combined[st_labels[n]].copy()
            ts.dropna(subset=['Obs'], inplace=True)
            ts = ts.astype(object).where(ts.notnull(), np.nan)
            ts = ts.astype(float)
        #    ts.to_csv('./point_data/P_'+st_labels[n].replace('*', '')+'.csv')

# %% Average recharge
day = combined.mean().mean()
month = combined.resample('M').sum().replace(0, np.nan).mean().mean()
year = combined.resample('Y').sum().replace(0, np.nan).mean().mean()

moth = combined.resample('M').agg(pd.Series.sum, min_count=2).mean().mean()
year = combined.resample('Y').agg(pd.Series.sum, min_count=2).mean().mean()
# %% Statistics for each station based on the extracted tables above
# Loading
# station_ts = [None] * len(st_labels)
# for n, st in enumerate(st_labels):
#     station_ts[n] = pd.read_csv('./point_data/P_'+st.replace('*', '')+'.csv',
#                                 index_col=0)
#     station_ts[n].index = pd.to_datetime(station_ts[n].index)

periods = ['Day', 'Month', 'Year']
stats = ['r', 'Bias', 'MAE', 'RMSE']
stats_tab = pd.DataFrame(columns=stats, index=products)

for per in periods:
    rmse = pd.DataFrame(np.zeros((len(products), len(st_labels))),
                        columns=st_labels, index=products)
    r = pd.DataFrame(np.zeros((len(products), len(st_labels))),
                     columns=st_labels, index=products)
    bias = pd.DataFrame(np.zeros((len(products), len(st_labels))),
                        columns=st_labels, index=products)
    mae = pd.DataFrame(np.zeros((len(products), len(st_labels))),
                       columns=st_labels, index=products)

    for n, st in enumerate(st_labels):
        for p, prod in enumerate(products):
            ts = station_ts[n]
            ts = ts.astype(object).where(ts.notnull(), np.nan)
            ts = ts.astype(float)

            # get data
            resampled = ts.resample(per[0]).sum()
            # resampled = resampled.replace(0, np.nan)
            res = resampled[[prod, 'Obs']].dropna()

            # calculation of statistics
            stats_tab.loc[prod, 'r'] = np.corrcoef(res['Obs'], res[prod])[0, 1]
            stats_tab.loc[prod, 'Bias'] = np.mean(res[prod]) - np.mean(res['Obs'])
            stats_tab.loc[prod, 'MAE'] = np.mean(np.abs(res[prod] - res['Obs']))
            se = (res['Obs'] - res[prod])**2
            mse = np.mean(se)
            stats_tab.loc[prod, 'RMSE'] = np.sqrt(mse)

        # write to file
        rmse.loc[:, st] = stats_tab['RMSE']
        r.loc[:, st] = stats_tab['r']
        bias.loc[:, st] = stats_tab['Bias']
        mae.loc[:, st] = stats_tab['MAE']

    with pd.ExcelWriter('./point_data/Stats_'+per+'.xlsx') as writer:
        rmse.to_excel(writer, sheet_name='RMSE')
        r.to_excel(writer, sheet_name='r')
        rmse.to_excel(writer, sheet_name='Bias')
        mae.to_excel(writer, sheet_name='MAE')
