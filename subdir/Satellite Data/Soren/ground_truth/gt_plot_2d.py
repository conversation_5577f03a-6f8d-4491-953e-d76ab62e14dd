# -*- coding: utf-8 -*-
"""
Created on Thu Aug 12 09:26:22 2021

@author: wqg436
"""
import pandas as pd
import geopandas as gp
import numpy as np
import os
import netCDF4 as nc
import datetime
from dateutil.relativedelta import relativedelta
import matplotlib.pyplot as plt
import matplotlib as mpl
import matplotlib.colors as colors
from matplotlib.colors import Normalize
from matplotlib.collections import LineCollection

# %% Loading shapefiles
# crs_utm = 'EPSG:32645'
# crs = 'EPSG:4326'
ws = gp.read_file('../../3.Basic System Data/01 Shapefiles/WatershedAtBigod.shp')

area = np.load('../area_ws_in_grid_P.npy')

# %% Loading observation data and specifying files
st_path = '../../4.Climate Data/Rainfall Gauge Stations June 2021/'

# time series
ts = pd.read_csv(st_path + 'final/rain_gauge_st_ts_2000-2021.csv',
                 index_col='Date')
ts.index = pd.to_datetime(ts.index)

# station infromation
stations = pd.read_csv(st_path + 'final/rain_gauge_info.csv')
geometry = gp.points_from_xy(stations.Lon, stations.Lat)
stations = gp.GeoDataFrame(stations, geometry=geometry, crs='epsg:4326')
stations.index = stations['Station']

# # Specifying nc file paths
# nc_path = '../clipped/P'
# nc_files = os.listdir(nc_path)
# products = [n.replace('PREC_', '').replace('.nc', '') for n in nc_files]
# nc_files = [nc_path + '/' + file for file in nc_files]

# # adding IMD
# nc_files.append('../../4.Climate Data//IMD_Rainfall_2000-2020_Bigod.nc')
# products.append('IMD')

# %% Load prec files and extracting mean annual rainfall
# mean_anns = [None]*len(nc_files)

# for n in range(len(nc_files)):
#     # opening file
#     ds = nc.Dataset(nc_files[n])
#     data = ds.variables['pre'][:]

#     # Calculate dates
#     t_unit = ds['time'].units
#     delta_unit = t_unit.split()[0]
#     date = np.array(t_unit.split()[2:][0].split('-'),
#                     dtype=int)
#     st_date = datetime.datetime(date[0], date[1], date[2])
#     t_ori = ds.variables['time'][:]
#     time = [st_date + relativedelta(**{delta_unit: t_ori[t]})
#             for t in range(t_ori.shape[0])]

#     # Calculate mean annual prec
#     years = [i for i in range(2000, 2021)]
#     ann = np.zeros((len(years), data.shape[1], data.shape[2]))
#     for i, year in enumerate(years):
#         idx = [i for i, t in enumerate(time) if t.year == year]
#         ann[i, :, :] = np.nansum(data[idx, :, :], axis=0).data
#     ann[ann == 0] = np.nan
#     mean_ann = np.nanmean(ann, axis=0)
#     mean_ann[area == 0] = np.nan
#     mean_anns[n] = mean_ann

# # extracting geographic information
# lat = ds.variables['lat'][:]
# lon = ds.variables['lon'][:]

# segs1 = np.stack((lon, lat), axis=2)
# segs1 = np.ma.masked_where(segs1 > 1000, segs1.data)
# segs2 = segs1.transpose(1, 0, 2)
# kwargs1 = {'color': 'k', 'lw': 0.1}

# # %% Plotting
# bounds = np.linspace(0.5, 1.5, 6)
# bounds = np.linspace(0.4, 1.6, 4)
# norm = colors.BoundaryNorm(boundaries=bounds, ncolors=256)
# cmap = mpl.cm.get_cmap('RdYlGn')
# leg_kwds = {'title': 'Sat/Obs', 'bbox_to_anchor': (1.7, 1.1)}

# fig, ax = plt.subplots(3, 3, figsize=(10, 7))
# plt.style.use('default')
# ax = ax.ravel()

# for n in range(len(nc_files)):
#     ax[n].add_collection(LineCollection(segs1, **kwargs1))
#     ax[n].add_collection(LineCollection(segs2, **kwargs1))
#     ws.plot(ax=ax[n], facecolor='none', edgecolor='orange', lw=1, zorder=100)

#     ax[n].axis('off')
#     title = nc_files[n].replace('PREC_', '').replace('.nc', '')
#     ax[n].set_title(title)

#     # Satelite data
#     c = ax[n].pcolormesh(lon, lat, mean_anns[n], vmin=500, vmax=900,
#                          cmap='inferno')

#     # Rain gauge stations
#     stations['sat'] = mean_anns[n][stations.row, stations.col]
#     stations['error'] = stations['sat'] / stations['obs']
#     legend = True if n == len(nc_files)-1 else False
#     h = stations.plot(ax=ax[n], column='error', cmap=cmap, scheme="User_Defined",
#                       norm=Normalize(0, len(bounds)), edgecolor='k',
#                       legend=legend, legend_kwds=leg_kwds,
#                       classification_kwds=dict(bins=bounds), zorder=101)
# # Legend
# h.get_legend().texts[0]._text = '< ' + str(bounds[0])
# h.get_legend().texts[-1]._text = str(bounds[-1]) + ' >'
# for i in range(0, len(bounds)-1):
#     h.get_legend().texts[i+1]._text = str(bounds[i]) + ' - ' + str(bounds[i+1])

# # colorbar
# cbaxes = fig.add_axes([0.5, 0.2, 0.4, 0.02])
# cb = plt.colorbar(c, label='Precipitation [mm/year]', orientation='horizontal',
#                   cax=cbaxes)
# cb.ax.xaxis.set_label_position('top')

# for n in range(n, len(ax)):
#     ax[n].axis('off')
# %% Average recharge
st_path = '../../4.Climate Data/Rainfall Gauge Stations June 2021/'
combined = pd.read_csv(st_path + 'final/rain_gauge_st_ts_2000-2021.csv',
                       index_col='Date')
combined.index = pd.to_datetime(combined.index)

# calculate
day = combined.mean().mean()
month = combined.resample('M').agg(pd.Series.sum, min_count=2).mean().mean()
year = combined.resample('Y').agg(pd.Series.sum, min_count=2).mean().mean()

ave_pre = [day, month, year]
# %% Stats individual ranking
periods = ['Day', 'Month', 'Year']
stats = ['r', 'Bias', 'MAE', 'RMSE']
cmaps = ['RdYlGn', 'RdYlGn_r', 'RdYlGn_r', 'RdYlGn_r']
cmap = mpl.cm.get_cmap('RdYlGn')
k = 3  # number of classes/jenks

per = 'Year'
p = periods.index(per)
stat = 'RMSE'
s = stats.index(stat)
leg_kwds = {'bbox_to_anchor': (0.8, -0.)}

fig, ax = plt.subplots(2, 4, figsize=(15, 7))
fig.suptitle('Stat: ' + stat + ', Period:' + per,
             weight='bold', size='x-large')
ax = ax.ravel()

df = pd.read_excel('./point_data/Stats_'+per+'.xlsx', sheet_name=stat,
                   index_col=0)
if stat == 'RMSE':
    df = df/ave_pre[p]
products = df.index.to_list()

for i, prod in enumerate(products):
    stations['c'] = df.loc[prod]
    h = stations.plot(ax=ax[i], column='c', cmap=cmap, scheme="NaturalBreaks",
                      edgecolor='k', k=k,
                      legend=True, legend_kwds=leg_kwds, zorder=101)
    ws.plot(ax=ax[i], facecolor='none', edgecolor='orange', lw=1, zorder=100)
    ax[i].axis('off')
    ax[i].set_title(prod, weight='bold', size='large')

for n in range(i+1, len(ax)):
    ax[n].axis('off')
# %% Stats same legend
periods = ['Day', 'Month', 'Year']
stats = ['r', 'Bias', 'MAE', 'RMSE']
cmaps = ['RdYlGn', 'RdYlGn_r', 'RdYlGn_r', 'RdYlGn_r']
k = 4  # number of classes/jenks

per = 'Year'
p = periods.index(per)
stat = 'RMSE'
s = stats.index(stat)
leg_kwds = {'bbox_to_anchor': (2.1, 1.2)}

for per in periods[:1]:
    for stat in stats:
        fig, ax = plt.subplots(2, 4, figsize=(10, 4))
        fig.suptitle('Stat: ' + stat + ', Period: ' + per,
                     weight='bold', size='x-large')
        ax = ax.ravel()

        df = pd.read_excel('./point_data/Stats_'+per+'.xlsx', sheet_name=stat,
                           index_col=0)
        # df.drop(['SM2RAIN_ASCAT'], inplace=True)
        if stat == 'RMSE':
            df = df/ave_pre[p]

        products = df.index.to_list()

        bins = np.linspace(df.min().min()+3*df.std().std(), df.max().max(), k)
        legend = [False]*6 + [True]
        for i, prod in enumerate(products):
            stations['c'] = df.loc[prod]
            h = stations.plot(ax=ax[i], column='c', cmap=cmaps[s],
                              scheme="User_Defined",
                              edgecolor='k', norm=Normalize(0, k),
                              classification_kwds=dict(bins=bins),
                              legend=legend[i], legend_kwds=leg_kwds,
                              zorder=101)
            ws.plot(ax=ax[i], facecolor='none', edgecolor='orange', lw=1,
                    zorder=100)
            ax[i].axis('off')
            ax[i].set_title(prod, weight='bold', size='large')

        for n in range(i+1, len(ax)):
            ax[n].axis('off')
        plt.savefig('./Figures/Map_'+stat+'_'+per+'.png', dpi=300)

# %% Ranking
periods = ['Day', 'Month', 'Year']
stats = ['r', 'Bias', 'MAE', 'RMSE']

ranks = pd.DataFrame()

per = 'Year'
p = periods.index(per)
stat = 'RMSE'
# s = stats.index(stat)
ascending = [False] + [True]*3
for stat in stats:
    ascending = False if stat == 'r' else True
    for per in periods:
        df = pd.read_excel('./point_data/Stats_'+per+'.xlsx', sheet_name=stat,
                           index_col=0)
        rank = df.rank(ascending=ascending)
        ranks[per + stat] = df.rank(ascending=ascending).sum(axis=1)
ranks.sum(axis=1)


fig, ax = plt.subplots(3, 3, figsize=(15, 10))
fig.suptitle('Stat: ' + stat + ', Period: ' + per,
             weight='bold', size='x-large')
ax = ax.ravel()

products = df.index.to_list()
k = len(products)
bins = np.linspace(1, k+1, k+1)
legend = [False]*6 + [True]
for i, prod in enumerate(products):
    stations['c'] = rank.T[prod]
    h = stations.plot(ax=ax[i], column='c', cmap='Spectral_r',
                      edgecolor='k', norm=Normalize(0, k),
                      categorical=True,
                      legend=legend[i], legend_kwds=leg_kwds, zorder=101)
    ws.plot(ax=ax[i], facecolor='none', edgecolor='orange', lw=1, zorder=100)
    ax[i].axis('off')
    ax[i].set_title(prod, weight='bold', size='large')

for n in range(i+1, len(ax)):
    ax[n].axis('off')
