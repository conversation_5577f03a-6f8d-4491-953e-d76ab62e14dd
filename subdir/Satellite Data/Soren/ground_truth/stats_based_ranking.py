# -*- coding: utf-8 -*-
"""
Created on Fri Aug 20 12:06:16 2021

@author: wqg436
"""
import pandas as pd
import os
import numpy as np
import matplotlib.pyplot as plt
import geopandas as gp
import datetime
from dateutil.relativedelta import relativedelta
import matplotlib as mpl
import matplotlib.colors as colors
from matplotlib.colors import Normalize
from matplotlib.collections import LineCollection
import netCDF4 as nc

# %% Ranking
periods = ['Day', 'Month', 'Year']
stats = ['r', 'Bias', 'MAE', 'RMSE']

ranks = pd.DataFrame()

per = 'Year'
p = periods.index(per)
stat = 'RMSE'
# s = stats.index(stat)
ascending = [False] + [True]*3
std_rank = pd.DataFrame()
for stat in stats:
    ascending = False if stat == 'r' else True
    for per in periods:
        df = pd.read_excel('./point_data/Stats_'+per+'.xlsx', sheet_name=stat,
                           index_col=0)
        rank = df.rank(ascending=ascending)
        ranks[per + stat] = rank.sum(axis=1)
        std_rank[per + stat] = df.std(axis=1).rank()
ranks.sum(axis=1)


# %%
st_path = '../../4.Climate Data/Rainfall Gauge Stations June 2021/'
stations = pd.read_csv(st_path + 'final/rain_gauge_info.csv')
geometry = gp.points_from_xy(stations.Lon, stations.Lat)
stations = gp.GeoDataFrame(stations, geometry=geometry, crs='epsg:4326')
stations.index = stations['Station']

ws = gp.read_file('../../3.Basic System Data/01 Shapefiles/WatershedAtBigod.shp')

area = np.load('../area_ws_in_grid_P.npy')
# %%
nc_path = './clipped/P/'
nc_files = os.listdir(nc_path)
n = 0
mean_anns = [None]*len(nc_files)
for n in range(len(nc_files)):
    # opening file
    fn = nc_path + '/' + nc_files[n]
    ds = nc.Dataset(fn)
    data = ds.variables['pre'][:]

    # Calculate dates
    t_unit = ds['time'].units
    delta_unit = t_unit.split()[0]
    date = np.array(t_unit.split()[2:][0].split('-'),
                    dtype=int)
    st_date = datetime.datetime(date[0], date[1], date[2])
    t_ori = ds.variables['time'][:]
    time = [st_date + relativedelta(**{delta_unit: t_ori[t]})
            for t in range(t_ori.shape[0])]

    # Calculate mean annual prec
    years = [i for i in range(2000, 2021)]
    ann = np.zeros((len(years), data.shape[1], data.shape[2]))
    for i, year in enumerate(years):
        idx = [i for i, t in enumerate(time) if t.year == year]
        ann[i, :, :] = np.nansum(data[idx, :, :], axis=0).data
    ann[ann == 0] = np.nan
    mean_ann = np.nanmean(ann, axis=0)
    mean_ann[area == 0] = np.nan
    mean_anns[n] = mean_ann

# extracting geographic information
lat = ds.variables['lat'][:]
lon = ds.variables['lon'][:]

segs1 = np.stack((lon, lat), axis=2)
segs1 = np.ma.masked_where(segs1 > 1000, segs1.data)
segs2 = segs1.transpose(1, 0, 2)
kwargs1 = {'color': 'k', 'lw': 0.1}
# %% Plotting
fig, ax = plt.subplots(2, 4, figsize=(10, 4))
fig.suptitle('Stat: ' + stat + ', Period: ' + per,
             weight='bold', size='x-large')
ax = ax.ravel()

products = df.index.to_list()
bins = np.linspace(1, len(products), len(products))
legend = [False]*6 + [True]
for i, prod in enumerate(products):
    stations['c'] = df.loc[prod]
    h = stations.plot(ax=ax[i], column='c', cmap=cmaps[s],
                      scheme="User_Defined",
                      edgecolor='k', norm=Normalize(0, k),
                      classification_kwds=dict(bins=bins),
                      legend=legend[i], legend_kwds=leg_kwds, zorder=101)
    ws.plot(ax=ax[i], facecolor='none', edgecolor='orange', lw=1, zorder=100)
    ax[i].axis('off')
    ax[i].set_title(prod, weight='bold', size='large')

ax[7].axis('off')
# Satelite data
h = ax.pcolormesh(lon, lat, mean_ann, vmin=500, vmax=900, cmap='Blues')
cbaxes = fig.add_axes([0.4, -0.1, 0.45, 0.03])
cb = plt.colorbar(h, label='Precipitation [mm/year]', orientation='horizontal',
                  cax=cbaxes)
cb.ax.xaxis.set_label_position('top')

# # Rain gauge stations
# bounds = np.linspace(0.7, 1.3, 4)
# norm = colors.BoundaryNorm(boundaries=bounds, ncolors=256)
# cmap = mpl.cm.get_cmap('RdYlGn')
# stations['error'] = stations['sat'] / stations['obs']

# leg_kwds = {'title': 'Rain gauge error', 'bbox_to_anchor': (0.3, -0.15)}

# h = stations.plot(ax=ax, column='error', cmap=cmap, scheme="User_Defined",
#                   norm=Normalize(0, len(bounds)), edgecolor='k',
#                   legend=True, legend_kwds=leg_kwds,
#                   classification_kwds=dict(bins=bounds), zorder=101)
# h.get_legend().texts[0]._text = '< ' + str(bounds[0])
# h.get_legend().texts[-1]._text = str(bounds[-1]) + ' >'
# for i in range(0, len(bounds)-1):
#     h.get_legend().texts[i+1]._text = str(bounds[i]) + ' - ' + str(bounds[i+1])