# -*- coding: utf-8 -*-
"""
Created on Fri Aug 13 08:16:31 2021

@author: wqg436
"""
import rasterio
import numpy as np
from osgeo import gdal
from shapely.geometry import Polygon, shape
from rtree import index
import fiona
import os
import netCDF4 as nc
import matplotlib.pyplot as plt
from rasterio.warp import calculate_default_transform, reproject, Resampling
from shapely.geometry import Polygon


def shp_geom_in_raster(shp_path, grid_path, poly_or_line):
    ''' area of polygon or length of line within raster cell '''
    shp = fiona.open(shp_path)  # load polygons
    grid = gdal.Open(grid_path)  # load raster

    # get info of the raster
    grid.GetProjection()
    info = grid.GetGeoTransform()
    spX = info[0]  # starting point
    spY = info[3]
    pixelSizeX = info[1]  # size X
    pixelSizeY = info[5]  # size Y
    cols = grid.RasterXSize
    rows = grid.RasterYSize

    rasterArray = np.zeros((rows, cols))

    # create spatial indeces for polygons of forests
    pol_idx = index.Index()
    for i, featA in enumerate(shp):
        pol_idx.insert(i, shape(featA['geometry']).bounds)

    for col in range(cols):  # and now the critical part
        for row in range(rows):  # col x row = each "pixel" cell
            # get corner coordinates of the pixel
            nw = [spX + col * pixelSizeX, spY + row * pixelSizeY]
            ne = [spX + (col+1) * pixelSizeX, spY + row * pixelSizeY]
            sw = [spX + col * pixelSizeX, spY + (row+1) * pixelSizeY]
            se = [spX + (col+1) * pixelSizeX, spY + (row+1) * pixelSizeY]
            # create shapely polygon of corners
            poly = Polygon([nw, ne, se, sw])

            # find intersection of bboxes
            for ints in pol_idx.intersection(poly.bounds):
                sub_shp = shape(shp[ints]['geometry'])
                if poly_or_line == 'poly':
                    # check if it really intersects
                    if poly.intersects(sub_shp.buffer(0)):
                        # add the area of intersection to array
                        rasterArray[row, col] = rasterArray[row, col] + \
                                                (poly.intersection(sub_shp.buffer(0)).area)
                elif poly_or_line == 'line':
                    if sub_shp.intersects(poly):  # if it really intersects
                        rasterArray[row, col] = rasterArray[row, col] + \
                                                (sub_shp.intersection(poly).length)
                                                # rasterArray[row, col] = round((rasterArray[row, col] / poly.area)*100)  
                                                # for each cell calculate the relative size of intersection polygons
    return rasterArray


def reproject_tif(file, new_file, dst_crs):
    # dst_crs = 'EPSG:4326'
    with rasterio.open(file) as src:
        transform, width, height = calculate_default_transform(
            src.crs, dst_crs, src.width, src.height, *src.bounds)
        kwargs = src.meta.copy()
        kwargs.update({
            'crs': dst_crs,
            'transform': transform,
            'width': width,
            'height': height
        })

        with rasterio.open(new_file, 'w', **kwargs) as dst:
            for i in range(1, src.count + 1):
                reproject(
                    source=rasterio.band(src, i),
                    destination=rasterio.band(dst, i),
                    src_transform=src.transform,
                    src_crs=src.crs,
                    dst_transform=transform,
                    dst_crs=dst_crs,
                    resampling=Resampling.nearest)


def reproject_tif2(file, new_file, dst_crs):
    with rasterio.open(file) as src:
        # left, bottom, right, top
        bounds = rasterio.warp.transform_bounds(src.crs, dst_crs,
                                                *src.bounds)
        array = src.read(1)
        kwargs = src.meta.copy()
        ny, nx = array.shape[0], array.shape[1]  # number of rows, columns
        xres = (bounds[2] - bounds[0]) / float(nx)
        yres = (bounds[3] - bounds[1]) / float(ny)

        transform = rasterio.Affine(xres, 0.0, bounds[0], 0.0, -yres,
                                    bounds[3])

        kwargs.update({
            'crs': dst_crs,
            'transform': transform,
            'width': nx,
            'height': ny
        })

        with rasterio.open(new_file, 'w', **kwargs) as dst:
            for i in range(1, src.count + 1):
                reproject(
                    source=rasterio.band(src, i),
                    destination=rasterio.band(dst, i),
                    src_transform=src.transform,
                    src_crs=src.crs,
                    dst_transform=transform,
                    dst_crs=dst_crs,
                    resampling=Resampling.nearest)


data_type = 'IMD'

utm_crs = 'EPSG:32645'
deg_crs = 'EPSG:4326'
crs = deg_crs if data_type == 'IMD' else utm_crs
ext = '' if data_type == 'IMD' else '_utm'
shp_path = '../3.Basic System Data/01 Shapefiles/WatershedAtBigod'+ext+'.shp'
poly_or_line = 'poly'
grid_path = './area_ws_in_grid_' + data_type + '.tif'

# opening file
if data_type == 'IMD':
    fn = '../4.Climate Data/IMD_Rainfall_2000-2020_Bigod.nc'
else:
    nc_path = './clipped/' + data_type
    nc_files = os.listdir(nc_path)
    n = 0
    fn = nc_path + '/' + nc_files[n]


ds = nc.Dataset(fn)
lat = ds.variables['lat'][:].data

# extracting geographic information and times
xc = ds.variables['xc'][:]
yc = ds.variables['yc'][:]
ds.close()

# extracting data arrays
ny, nx = yc.shape[0], xc.shape[0]  # number of rows, columns

xmin, ymin, xmax, ymax = [xc.min(), yc.min(), xc.max(), yc.max()]
xres = (xmax - xmin) / (nx-1)
yres = (ymax - ymin) / (ny-1)

# create meta data
data = lat
kwargs = {'driver': 'GTiff',
          'dtype': str(data.dtype),
          'nodata': 0.0,
          'width': nx,
          'height': ny,
          'count': 1,
          'crs': deg_crs,
          # second cellsize should be negative if in espg:4326
          'transform': rasterio.Affine(xres, 0.0, xmin,
                                       0.0, -yres, ymax)}
# write to tif file
with rasterio.open(grid_path, 'w', **kwargs) as dst:
    dst.write(data, 1)

# if data_type == 'IMD':
#     reproject_tif2(grid_path, grid_path, utm_crs)

area = shp_geom_in_raster(shp_path, grid_path, poly_or_line)
plt.imshow(area)

# write to tif file
kwargs.update({'dtype': str(area.dtype)})
with rasterio.open(grid_path, 'w', **kwargs) as dst:
    dst.write(area, 1)
np.save(grid_path[:-4] + '.npy', area)
os.remove(grid_path)
