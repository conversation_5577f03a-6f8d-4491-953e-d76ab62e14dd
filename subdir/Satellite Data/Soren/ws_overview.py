# -*- coding: utf-8 -*-
"""
Created on Thu Jul  1 11:49:10 2021

@author: wqg436
"""

import geopandas as gp
import matplotlib.pyplot as plt
import netCDF4 as nc
import os
from shapely.geometry import Polygon
from matplotlib.patches import Patch
import numpy as np
from matplotlib.collections import LineCollection

crs_utm = 'EPSG:32645'
crs = 'EPSG:4326'

# %% Extent of Sorens data
data_type = 'P'
var = 'pre' if data_type == 'P' else 'et'

path = './original/' + data_type
files = os.listdir(path)

fn = path + '/' + files[0]
ds = nc.Dataset(fn)
lat = ds.variables['lat'][:]
lon = ds.variables['lon'][:]
xc = ds.variables['xc'][:]
yc = ds.variables['yc'][:]

p = []
p += list(zip(lon[:, 0], lat[:, 0]))  # west
p += list(zip(lon[-1, :], lat[-1, :]))  # south
p += list(zip(lon[:, -1], lat[:, -1]))[::-1]  # east
p += list(zip(lon[0, :], lat[0, :]))[::-1]   # north

polygon_geom = Polygon(p)
polygon = gp.GeoDataFrame(index=[0], crs=crs, geometry=[polygon_geom])
polygon.plot()
# %% Loading shapedata
world = gp.read_file('../../Resources/Figure/Overview maps/ne_10m_admin_1_states_provinces/ne_10m_admin_1_states_provinces.shp')
india = world[world['adm0_a3'] == 'IND']
ws = gp.read_file('../3.Basic System Data/01 Shapefiles/WatershedAtBigod.shp')

# %% Plotting
# utm = Polygon([[xc.min(), yc.min()],
#                [xc.max(), yc.min()],
#                [xc.max(), yc.max()],
#                [xc.min(), yc.max()]])
# utm = gp.GeoDataFrame(geometry=[utm], crs=crs_utm)
# utm = utm.to_crs(crs)
plt.style.use('default')
kwargs = [{'facecolor': 'gainsboro', 'label': 'India'},
          {'facecolor': 'seagreen', 'label': 'Rajasthan'},
          {'facecolor': 'orange', 'label': 'Bigod watershed'},
          {'facecolor': 'none', 'edgecolor': 'r', 'label': 'Sørens data'}]

fig, ax = plt.subplots()
india.plot(ax=ax, **kwargs[0])
india[india['name'] == 'Rajasthan'].plot(ax=ax, **kwargs[1])
ws.plot(ax=ax, **kwargs[2])
polygon.plot(ax=ax, **kwargs[3])
# utm.plot(ax=ax, facecolor='none', edgecolor='k')

lgd = [Patch(**kwargs[i]) for i in range(len(kwargs))]
ax.legend(handles=lgd, loc='center', bbox_to_anchor=(1.35, 0.85))

plt.savefig('./Figures/Overview map.png', bbox_inches='tight', dpi=300)

# %% Grid of P vs ET
titles = ['Precipitation', 'Evapotranspiration']
variable = ['pre', 'et']
nc_paths = ['./clipped/P', './clipped/ET']
kwargs1 = {'color': 'k', 'lw': 0.1}
i = 1

n = 0
fig, ax = plt.subplots(1, 2)
for i in range(2):
    # opening file
    nc_files = os.listdir(nc_paths[i])

    fn = nc_paths[i] + '/' + nc_files[n]
    ds = nc.Dataset(fn)
    data = ds.variables[variable[i]][:]

    # extracting geographic information
    lat = ds.variables['lat'][:]
    lon = ds.variables['lon'][:]

    segs1 = np.stack((lon, lat), axis=2)
    segs2 = segs1.transpose(1, 0, 2)

    # plotting
    ax[i].add_collection(LineCollection(segs1, **kwargs1))
    ax[i].add_collection(LineCollection(segs2, **kwargs1))
    ws.plot(ax=ax[i], facecolor='none', edgecolor='orange', lw=1, zorder=100)
    ax[i].set_xlim(73.25, 75.3)
    ax[i].set_ylim(24.25, 25.45)
    ax[i].set_aspect('equal')
    ax[i].set_xlabel('Longitude')
    ax[i].set_ylabel('Latitude')
    ax[i].set_title(titles[i])
plt.tight_layout()
plt.savefig('./Figures/Sat_product_grid.png', bbox_inches='tight', dpi=300)
