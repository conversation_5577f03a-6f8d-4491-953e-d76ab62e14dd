# -*- coding: utf-8 -*-
"""
Created on Wed Jun 30 10:23:06 2021

@author: wqg436

CRS notes:
    EPSG:4326 WGS 84 world (long+lat)
    EPSG:32645 WGS 84 / UTM zone 45N
check ws_overview where plotted
"""

import netCDF4 as nc
import os
import rasterio
import fiona
import numpy as np
import rasterio.mask
import geopandas as gp
from shapely.geometry import Polygon

data_type = 'P'
var = 'pre' if data_type == 'P' else 'et'
path = './original/' + data_type
files = os.listdir(path)

# %% Create shape for clipping
crs_utm = 'EPSG:32645'
crs = 'EPSG:4326'
shp_ff = '../3.Basic System Data/01 Shapefiles/'

ws = gp.read_file(shp_ff + 'WatershedAtBigod.shp')
ws = ws.to_crs(crs=crs_utm)
# ws = gp.to_file(shp_ff + 'WatershedAtBigod_utm.shp')

# Create bounding box around Bigod watershed with buffer of 20 km
b = ws.total_bounds
ws = Polygon([[b[2], b[1]],
             [b[0], b[1]],
             [b[0], b[3]],
             [b[2], b[3]]])
ws = gp.GeoDataFrame(geometry=[ws], crs=crs_utm)
ws = ws.buffer(10*1000, join_style=2)  # 20 km == 2 cells

# Saving
clip_shp = shp_ff + 'Sat_clip.shp'
ws.to_file(clip_shp)

# %% Writing netcdf file to tif
for i in range(len(files)):
    # opening file
    fn = path + '/' + files[i]
    ds = nc.Dataset(fn)

    # extract keys
    dimensions = list(ds.dimensions.keys())
    variables = list(ds.variables.keys())
    data_key = [var for var in variables if var not in dimensions]
    lon_key = [dim for dim in variables if 'lon' in dim.lower()]
    lat_key = [dim for dim in variables if 'lat' in dim.lower()]
    time_key = [dim for dim in dimensions if 'time' in dim.lower()]

    # extracting geographic information and times
    xc = ds.variables['xc'][:]
    yc = ds.variables['yc'][:]
    time_in = ds[time_key[0]][:]

    # extracting data arrays
    ny, nx = yc.shape[0], xc.shape[0]  # number of rows, columns

    xmin, ymin, xmax, ymax = [xc.min(), yc.min(), xc.max(), yc.max()]
    xres = (xmax - xmin) / (nx-1)
    yres = (ymax - ymin) / (ny-1)

    # write to tif file
    out_images = [None]*len(data_key)
    out_metas = [None]*len(data_key)
    for d in range(len(data_key)):  # loop through data_keys
        data = ds[data_key[d]][:]

        # create meta data
        count = data.shape[0] if len(data.shape) > 2 else 1
        kwargs = {'driver': 'GTiff',
                  'dtype': str(data.dtype),
                  'nodata': 0.0,
                  'width': nx,
                  'height': ny,
                  'count': count,
                  'crs': crs_utm,
                  # second cellsize should be negative if in espg:4326
                  'transform': rasterio.Affine(xres, 0.0, xmin,
                                               0.0, -yres, ymax)}
        bands = [i for i in range(1, count+1)] if count > 1 else 1
        raster_file = './test.tif'

        with rasterio.open(raster_file, 'w', **kwargs) as dst:
            dst.write(data, bands)

        # % Clipping tif file to watershed
        with fiona.open(clip_shp, "r") as shpfile:
            shapes = [feature["geometry"] for feature in shpfile]
        with rasterio.open(raster_file) as src:
            out_image, out_transform = rasterio.mask.mask(src, shapes,
                                                          crop=True,
                                                          all_touched=True)
            out_meta = src.meta
        out_image = np.ma.masked_where(out_image == 0, out_image)
        out_images[d] = out_image

        out_meta.update({"driver": "GTiff",
                         "height": out_image.shape[1],
                         "width": out_image.shape[2],
                         "transform": out_transform})
        out_metas[d] = out_meta
        # with rasterio.open('test2.tif', 'w', **out_meta) as dst:
        #     dst.write(out_image[0, :, :], bands)

    # % Writing to new netcdf
    new_fn = './clipped/'+data_type+'/' + files[i]
    ds_new = nc.Dataset(new_fn, 'w', format='NETCDF4')

    # variables
    time = ds_new.createDimension('time', data.shape[0])
    yc = ds_new.createDimension('yc', out_image.shape[1])
    xc = ds_new.createDimension('xc', out_image.shape[2])

    times = ds_new.createVariable('time', 'f4', ('time',))
    xcs = ds_new.createVariable('xc', 'f4', ('xc',))
    ycs = ds_new.createVariable('yc', 'f4', ('yc',))

    # data
    d = 1  # lon
    value0 = ds_new.createVariable(data_key[d], 'f4', ('yc', 'xc'))
    value0.units = ds[data_key[d]].units
    value0[:] = out_images[d][0, :, :]
    d = 0  # lat
    value1 = ds_new.createVariable(data_key[d], 'f4', ('yc', 'xc'))
    value1.units = ds[data_key[d]].units
    value1[:] = out_images[d][0, :, :]
    d = 2
    value2 = ds_new.createVariable(data_key[d], 'f4', ('time', 'yc', 'xc'))
    value2.units = ds[data_key[d]].units
    value2[:] = out_images[d]

    # Assign units
    times.units = ds[time_key[0]].units

    # Assign values to variables
    times[:] = time_in

    xcs[:] = np.array([out_transform[2]+out_transform[0]*f
                       for f in range(out_image.shape[2])])
    ycs[:] = np.array([out_transform[5]+out_transform[4]*f
                       for f in range(out_image.shape[1])])
    # Close file
    ds_new.close()

os.remove('test.tif')
