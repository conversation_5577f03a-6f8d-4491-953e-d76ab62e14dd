/*
    
TemplateMo 556 Catalog-Z

https://templatemo.com/tm-556-catalog-z

*/

#loader-wrapper {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1001;
}
#loader {
    display: block;
    position: relative;
    left: 50%;
    top: 50%;
    width: 150px;
    height: 150px;
    margin: -75px 0 0 -75px;
    border-radius: 50%;
    border: 3px solid transparent;
    border-top-color: #3498db;

    -webkit-animation: spin 2s linear infinite; /* Chrome, Opera 15+, Safari 5+ */
    animation: spin 2s linear infinite; /* Chrome, Firefox 16+, IE 10+, Opera */

    z-index: 1002;
}

    #loader:before {
        content: "";
        position: absolute;
        top: 5px;
        left: 5px;
        right: 5px;
        bottom: 5px;
        border-radius: 50%;
        border: 3px solid transparent;
        border-top-color: #e74c3c;

        -webkit-animation: spin 3s linear infinite; /* Chrome, Opera 15+, <PERSON>fari 5+ */
        animation: spin 3s linear infinite; /* Chrome, Firefox 16+, IE 10+, Opera */
    }

    #loader:after {
        content: "";
        position: absolute;
        top: 15px;
        left: 15px;
        right: 15px;
        bottom: 15px;
        border-radius: 50%;
        border: 3px solid transparent;
        border-top-color: #f9c922;

        -webkit-animation: spin 1.5s linear infinite; /* Chrome, Opera 15+, Safari 5+ */
          animation: spin 1.5s linear infinite; /* Chrome, Firefox 16+, IE 10+, Opera */
    }

    @-webkit-keyframes spin {
        0%   { 
            -webkit-transform: rotate(0deg);  /* Chrome, Opera 15+, Safari 3.1+ */
            -ms-transform: rotate(0deg);  /* IE 9 */
            transform: rotate(0deg);  /* Firefox 16+, IE 10+, Opera */
        }
        100% {
            -webkit-transform: rotate(360deg);  /* Chrome, Opera 15+, Safari 3.1+ */
            -ms-transform: rotate(360deg);  /* IE 9 */
            transform: rotate(360deg);  /* Firefox 16+, IE 10+, Opera */
        }
    }
    @keyframes spin {
        0%   { 
            -webkit-transform: rotate(0deg);  /* Chrome, Opera 15+, Safari 3.1+ */
            -ms-transform: rotate(0deg);  /* IE 9 */
            transform: rotate(0deg);  /* Firefox 16+, IE 10+, Opera */
        }
        100% {
            -webkit-transform: rotate(360deg);  /* Chrome, Opera 15+, Safari 3.1+ */
            -ms-transform: rotate(360deg);  /* IE 9 */
            transform: rotate(360deg);  /* Firefox 16+, IE 10+, Opera */
        }
    }

    #loader-wrapper .loader-section {
        position: fixed;
        top: 0;
        width: 70%;
        height: 100%;
        background: #222222;
        z-index: 1001;
        -webkit-transform: translateX(0);  /* Chrome, Opera 15+, Safari 3.1+ */
        -ms-transform: translateX(0);  /* IE 9 */
        transform: translateX(0);  /* Firefox 16+, IE 10+, Opera */
    }

    #loader-wrapper .loader-section.section-left {
        left: 0;
    }

    #loader-wrapper .loader-section.section-right {
        right: 0;
    }

    /* Loaded */
    .loaded #loader-wrapper .loader-section.section-left {
        -webkit-transform: translateX(-100%);  /* Chrome, Opera 15+, Safari 3.1+ */
            -ms-transform: translateX(-100%);  /* IE 9 */
                transform: translateX(-100%);  /* Firefox 16+, IE 10+, Opera */

        -webkit-transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);  
                transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
    }

    .loaded #loader-wrapper .loader-section.section-right {
        -webkit-transform: translateX(100%);  /* Chrome, Opera 15+, Safari 3.1+ */
            -ms-transform: translateX(100%);  /* IE 9 */
                transform: translateX(100%);  /* Firefox 16+, IE 10+, Opera */

-webkit-transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);  
        transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
    }
    
    .loaded #loader {
        opacity: 0;
        -webkit-transition: all 0.3s ease-out;  
                transition: all 0.3s ease-out;
    }
    .loaded #loader-wrapper {
        visibility: hidden;

        -webkit-transform: translateY(-100%);  /* Chrome, Opera 15+, Safari 3.1+ */
            -ms-transform: translateY(-100%);  /* IE 9 */
                transform: translateY(-100%);  /* Firefox 16+, IE 10+, Opera */

        -webkit-transition: all 0.3s 1s ease-out;  
                transition: all 0.3s 1s ease-out;
    }

p { line-height: 1.8; }

a { 
    text-decoration: none;
    transition: all 0.3s ease; 
}
a:hover { color:#009999; }
.container-fluid { max-width: 1770px; }
.tm-container-small { max-width: 1050px; }
.btn { border-radius: 0; }
.btn-primary {
    background-color: #009999;
    border: 0;
    border-radius: 5px;
    padding: 12px 50px 14px;
    font-size: 1.2rem;
}

.btn-primary:hover,
.btn-primary:focus {
    background-color:#086969;
}

.btn-primary.disabled, 
.btn-primary:disabled {
    background-color: #009999;
    pointer-events: all;
    cursor: not-allowed;
}

.form-control {
	padding: 10px 20px;
	width: 100%;
	height: auto;
}

.form-group { margin-bottom: 25px; }
.tm-btn-big { padding: 12px 90px 14px; }

.navbar-toggler:focus { box-shadow: none; }
.navbar-brand {
    color: #3399CC;
    font-size: 1.2rem;
}

.navbar-brand i { font-size: 1.5rem; }

.tm-hero {
    min-height: 250px;
    background: transparent;
}

.tm-mt-60 { margin-top: 60px; }
.tm-mb-50 { margin-bottom: 50px; }
.tm-mb-74 { margin-bottom: 74px; }
.tm-mb-90 { margin-bottom: 50px; }
.tm-text-primary { color: #009999; }
.tm-text-secondary { color: #CC6699; }
a.tm-text-primary:hover { color:#666666; }
.tm-bg-gray { background-color: #EEEEEE; }
.tm-input-paging {
    width: 40px;
    border-radius: 0;
    border: 1px solid #CCCCCC;
    background: #f4f4f4;
    text-align: center;
}

.nav-item { margin-right: 30px; }
.nav-item:last-child { margin-right: 0; }

.nav-link {
    color: #666666;
    border-bottom: 4px solid transparent;
    font-size: 1.2rem;
}

.nav-link-1.active,
.nav-link-1:hover {
    border-color: #33CCFF;
}

.nav-link-2.active,
.nav-link-2:hover {
    border-color: #FF6666;
}

.nav-link-3.active,
.nav-link-3:hover {
    border-color: #33CC66;
}

.nav-link-4.active,
.nav-link-4:hover {
    border-color: #CC66CC;
}

.tm-search-input {
    width: 360px;
    border-radius: 0;
    padding: 12px 15px;
    color: #009999;
    border: none;
}

.tm-search-input:focus {
    border-color: #009999;
    box-shadow: 0 0 0 0.25rem rgb(0 153 153 / 0.25);
}

.tm-search-input::placeholder { /* Chrome, Firefox, Opera, Safari 10.1+ */
    color: #009999;
    opacity: 1; /* Firefox */
}
  
.tm-search-input::-ms-input-placeholder { /* Microsoft Edge */
    color: #009999;
}

.tm-search-btn {
    color: white;
    background-color: #009999;
    border: none;
    width: 100px;
    height: 50px;
    margin-left: -1px;
}

p, .tm-text-gray { color: #999; }
.tm-text-gray-light { color: #CCC; }
.tm-text-gray-dark { color: #666; }

.tm-video-item {
    position: relative;
    overflow: hidden;
    text-align: center;
    cursor: pointer;
}

.tm-video-item img {
    position: relative;
    display: block;
    min-height: 100%;
    max-width: 100%;
    opacity: 0.8;
}

.tm-video-item figcaption {
    padding: 2em;
    color: #fff;
    text-transform: uppercase;
    font-size: 1.25em;
    -webkit-backface-visibility: hidden;
	backface-visibility: hidden;
}

.tm-video-item figcaption::before,
.tm-video-item figcaption::after {
    pointer-events: none;
}

.tm-video-item figcaption,
.tm-video-item figcaption > a {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.tm-video-item figcaption > a {
	z-index: 1000;
	text-indent: 200%;
	white-space: nowrap;
	font-size: 0;
	opacity: 0;
}

.tm-video-item h2 {
    word-spacing: -0.15em;
    font-weight: 200;
}

.tm-video-item h2,
.tm-video-item p {
    margin: 0;
}

.tm-video-item p {
    letter-spacing: 1px;
    font-size: 60.5%;
}

.tm-gallery div.d-block { animation: show .5s ease; }

@keyframes show {
    0% {
    	opacity: 0;
    	transform: scale(0.9);
    }
    100% {
    	opacity: 1;
    	transform: scale(1);
    }
}

.tm-paging-link {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 60px;
    height: 60px;
    font-size: 1.1rem;
    color: #999;
    background-color: #EEEEEE;
    margin: 10px;
    border-radius: 5px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.tm-paging-link:hover,
.tm-paging-link.active {
    background-color: #009999;
    color: #fff; 
}

/*---------------*/
/***** Ming *****/
/*---------------*/

figure.effect-ming {
	background: #adadad;
}

figure.effect-ming img {
	opacity: 0.9;
	-webkit-transition: opacity 0.35s;
	transition: opacity 0.35s;
}

figure.effect-ming figcaption::before {
	position: absolute;
	top: 30px;
	right: 30px;
	bottom: 30px;
	left: 30px;
	border: 2px solid #fff;
	box-shadow: 0 0 0 30px rgba(255,255,255,0.2);
	content: '';
	opacity: 0;
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	-webkit-transform: scale3d(1.4,1.4,1);
	transform: scale3d(1.4,1.4,1);
}

figure.effect-ming h2 {
    font-size: 1em;
	opacity: 0;
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	-webkit-transform: scale(1.5);
	transform: scale(1.5);
}

figure.effect-ming:hover figcaption::before,
figure.effect-ming:hover h2 {
	opacity: 1;
	-webkit-transform: scale3d(1,1,1);
	transform: scale3d(1,1,1);
}

figure.effect-ming:hover figcaption { background-color: rgba(58,52,42,0); }
figure.effect-ming:hover img { opacity: 0.4; }

.tm-footer-links li {
    list-style: none;
    margin-bottom: 5px;    
}

.tm-footer-links li a { color: #999999; }
.tm-footer-links li a:hover { color: #009999; }

.tm-social-links li {
    list-style: none;
    margin-right: 15px;
}

.tm-social-links li:last-child { margin-right: 0; }

.tm-social-links li a  {
    color: #999999;
    width: 44px;
    height: 44px;
    display: flex;
    background-color: #fff;
    align-items: center;
    justify-content: center;
}

.tm-social-links li a:hover {
    color: #fff;
    background-color: #009999;
}

.tm-footer { font-size: 0.95rem; }
.tm-footer-title { font-size: 1.4rem; }

/* Videos */
#tm-video-container {
    max-height: 400px;
    overflow: hidden;
    background-color: #333;
    margin-bottom: 90px;
    position: relative;
}

#tm-video {
    display: block;
    width: 100%;
    height: auto;
}

#tm-video-control-button {
    position: absolute;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    color: #e1e1e1;
}

.tm-video-details {
    height: 100%;
    padding: 40px;
}

/* About */
.tm-row-1640 { max-width: 1640px; }
.tm-about-2-col { max-width: 716px; }
.tm-about-3-col { max-width: 540px; }

.tm-about-2-col,
.tm-about-3-col {
    margin-bottom: 50px;
}

.tm-about-icon-container {
    width: 150px;
    height: 150px;
    border: 1px solid #009999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tm-about-img-text { max-width: 940px; }

/* Contact */
select.form-control { border-radius: 0; }
select.form-control option { height: 30px; }

select:not([multiple]) {
    -webkit-appearance: none;
    -moz-appearance: none;
    background-position: right 15px center;
    background-repeat: no-repeat;
    background-image: url(../img/select-arrow.png);
    padding: 14px 20px;
    padding-right: 20px;
    color: #666;
}

.mapouter{
    position:relative;
    height:480px;
    width:100%;
    max-width: 480px;
}

.gmap-canvas {
    overflow:hidden;
    background:none!important;
    height:480px;
    width:100%;
}

.tm-contact-form { max-width: 420px; }
.tm-address-col { max-width: 520px; }
.tm-contacts { padding-left: 0; }

.tm-contacts li {
    list-style: none;
    margin-bottom: 20px;
}

.tm-contacts li a i { width: 30px; }
.tm-social { display: flex; }

.tm-social li {
    list-style: none;
    margin-bottom: 20px;
    margin-right: 15px;
}

.tm-social li a i {
    width: 40px;
    height: 40px;
    color: #666666;
    background-color: #EEEEEE;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.tm-social li a:hover i {
    color: #fff;
    background-color: #009999;
}

.form-control,
input:-internal-autofill-selected,
select:not([multiple]) {
    color: #009999 !important;
}

.form-control::placeholder { /* Chrome, Firefox, Opera, Safari 10.1+ */
    color:  #009999;
    opacity: 1; /* Firefox */
}

@media (max-width: 991px) {
    .navbar-collapse {
        position: fixed;
        top: 50px;
        right: 0px;
        background: white;
        width: 150px;
        padding: 15px;
    }

    .tm-container-content { max-width: 870px; }
    .tm-contact-form, .tm-address-col, .mapouter { max-width: 100%; }
    .tm-people-row { max-width: 900px; }
}

@media (max-width: 767px) {
    .tm-paging-col { flex-direction: column; }
    .tm-paging { flex-wrap: wrap; }
    .tm-about-img-text { max-width: 640px; }
    .tm-about-2-col, .tm-about-3-col { margin-bottom: 50px; }
}

@media (max-width: 575px) {
    .tm-container-content { max-width: 420px; }

    .tm-search-form {
        padding-left: 15px;
        padding-right: 15px;
        width: 100%;
        justify-content: center;
    }

    .tm-search-input {
        width: 100%;
        max-width: 360px;
    }

    .tm-people-row { max-width: 420px; }
}

@media (max-width: 400px) {
    .tm-btn-big { padding: 12px 50px 14px; }
}

@media (max-width: 334px) {
    .tm-social-links {
        flex-wrap: wrap;
        justify-content: start !important;
    }

    .tm-social-links li { margin-right: 3px; }
}