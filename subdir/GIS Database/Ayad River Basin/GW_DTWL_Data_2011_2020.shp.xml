<?xml version="1.0" encoding="UTF-8"?>
<metadata xml:lang="en"><Esri><CreaDate>20220628</CreaDate><CreaTime>14440000</CreaTime><ArcGISFormat>1.0</ArcGISFormat><SyncOnce>FALSE</SyncOnce><DataProperties><lineage><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220217" Time="123340">CalculateField DTWL_11_20_v2 GroundElev [grid_code] VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220217" Time="130533">CalculateField DTWL_11_20_v2 GE_Pre11 "[GroundElev] - [GE_Pre11]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220217" Time="130543">CalculateField DTWL_11_20_v2 GE_Pre12 "[GroundElev] - [GE_Pre12]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220217" Time="130551">CalculateField DTWL_11_20_v2 GE_Pre13 "[GroundElev] - [GE_Pre13]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220217" Time="130637">CalculateField DTWL_11_20_v2 GE_Pre11 "[GroundElev] - [Pre11]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220217" Time="130646">CalculateField DTWL_11_20_v2 GE_Pre12 "[GroundElev] - [Pre12]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220217" Time="130655">CalculateField DTWL_11_20_v2 GE_Pre13 "[GroundElev] - [Pre13]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220217" Time="130704">CalculateField DTWL_11_20_v2 GE_Pre14 "[GroundElev] - [Pre14]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220217" Time="130712">CalculateField DTWL_11_20_v2 GE_Pre15 "[GroundElev] - [Pre15]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220217" Time="130721">CalculateField DTWL_11_20_v2 GE_Pre16 "[GroundElev] - [Pre16]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220217" Time="130730">CalculateField DTWL_11_20_v2 GE_Pre17 "[GroundElev] - [Pre17]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220217" Time="130739">CalculateField DTWL_11_20_v2 GE_Pre18 "[GroundElev] - [Pre18]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220217" Time="130748">CalculateField DTWL_11_20_v2 GE_Pre19 "[GroundElev] - [Pre19]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220217" Time="130801">CalculateField DTWL_11_20_v2 GE_Pre20 "[GroundElev] - [Pre20]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220217" Time="130830">CalculateField DTWL_11_20_v2 GE_Post11 "[GroundElev] - [Post11]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220217" Time="130841">CalculateField DTWL_11_20_v2 GE_Post12 "[GroundElev] - [Post12]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220217" Time="130850">CalculateField DTWL_11_20_v2 GE_Post13 "[GroundElev] - [Post13]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220217" Time="130859">CalculateField DTWL_11_20_v2 GE_Post14 "[GroundElev] - [Post14]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220217" Time="130909">CalculateField DTWL_11_20_v2 GE_Post15 "[GroundElev] - [Post15]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220217" Time="130921">CalculateField DTWL_11_20_v2 GE_Post16 "[GroundElev] - [Post16]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220217" Time="130928">CalculateField DTWL_11_20_v2 GE_Post17 "[GroundElev] - [Post17]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220217" Time="130935">CalculateField DTWL_11_20_v2 GE_Post18 "[GroundElev] - [Post18]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220217" Time="130943">CalculateField DTWL_11_20_v2 GE_Post19 "[GroundElev] - [Post19]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220217" Time="130953">CalculateField DTWL_11_20_v2 GE_Post20 "[GroundElev] - [Post20]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\DeleteField" Date="20220628" Time="120311">DeleteField GW_DTWL_Data_2011_2020_WRT_Topography_89 GroundElev;GE_Pre11;GE_Post11;GE_Pre12;GE_Post12;GE_Pre13;GE_Post13;GE_Pre14;GE_Post14;GE_Pre15;GE_Post15;GE_Pre16;GE_Post16;GE_Pre17;GE_Post17;GE_Pre18;GE_Post18;GE_Pre19;GE_Post19;GE_Pre20;GE_Post20</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220628" Time="121033">CalculateField GW_DTWL_Data_2011_2020_WRT_Topography_89 Pre ( [Pre11] + [Pre12] + [Pre13] + [Pre14] + [Pre15] + [Pre16] + [Pre17] + [Pre18] + [Pre19] + [Pre20])/10 VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220628" Time="121335">CalculateField GW_DTWL_Data_2011_2020_WRT_Topography_89 Post ( [Post11] + [Post12] + [Post13] + [Post14] + [Post15] + [Post16] + [Post17] + [Post18] + [Post19] + [Post20])/10 VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220628" Time="143829">CalculateField GW_DTWL_Data_2011_2020_WRT_Topography_89 F2011 "[Pre11] - [Post11]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220628" Time="143841">CalculateField GW_DTWL_Data_2011_2020_WRT_Topography_89 F2012 "[Pre12] - [Post12]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220628" Time="143852">CalculateField GW_DTWL_Data_2011_2020_WRT_Topography_89 F2013 "[Pre13] - [Post13]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220628" Time="143905">CalculateField GW_DTWL_Data_2011_2020_WRT_Topography_89 F2014 "[Pre14] - [Post14]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220628" Time="143916">CalculateField GW_DTWL_Data_2011_2020_WRT_Topography_89 F2015 "[Pre15] - [Post15]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220628" Time="143925">CalculateField GW_DTWL_Data_2011_2020_WRT_Topography_89 F2016 "[Pre16] - [Post16]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220628" Time="143937">CalculateField GW_DTWL_Data_2011_2020_WRT_Topography_89 F2017 "[Pre17] - [Post17]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220628" Time="143959">CalculateField GW_DTWL_Data_2011_2020_WRT_Topography_89 F2018 "[Pre18] - [Post18]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220628" Time="144017">CalculateField GW_DTWL_Data_2011_2020_WRT_Topography_89 F2019 "[Pre19] - [Post19]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220628" Time="144035">CalculateField GW_DTWL_Data_2011_2020_WRT_Topography_89 F2020 "[Pre20] - [Post20]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220628" Time="144159">CalculateField GW_DTWL_Data_2011_2020_WRT_Topography_89 F2020 "0" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220628" Time="144207">CalculateField GW_DTWL_Data_2011_2020_WRT_Topography_89 F2019 "0" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220628" Time="144217">CalculateField GW_DTWL_Data_2011_2020_WRT_Topography_89 F2018 "0" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220628" Time="144234">CalculateField GW_DTWL_Data_2011_2020_WRT_Topography_89 F2016 "0" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220628" Time="144540">CalculateField GW_DTWL_Data_2011_2020_89A FLU11 "[Pre11] - [Post11]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220628" Time="144731">CalculateField GW_DTWL_Data_2011_2020_89A FLU2012 "[Pre12] - [Post12]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220628" Time="144929">CalculateField GW_DTWL_Data_2011_2020_89A FLU2013 "[Pre13] - [Post13]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220628" Time="145046">CalculateField GW_DTWL_Data_2011_2020_89A FLU2014 "[Pre14] - [Post14]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220628" Time="145159">CalculateField GW_DTWL_Data_2011_2020_89A FLU2015 "[Pre15] - [Post15]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220628" Time="145326">CalculateField GW_DTWL_Data_2011_2020_89A FLU2016 "[Pre16] - [Post16]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220628" Time="145344">CalculateField GW_DTWL_Data_2011_2020_89A FLU2016 "0" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220628" Time="145631">CalculateField GW_DTWL_Data_2011_2020_89A FLU2017 "[Pre17] - [Post17]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220628" Time="145911">CalculateField GW_DTWL_Data_2011_2020_89A FLU2018 "[Pre18] - [Post18]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220628" Time="145925">CalculateField GW_DTWL_Data_2011_2020_89A FLU2018 "0" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220628" Time="150055">CalculateField GW_DTWL_Data_2011_2020_89A FLU2019 "[Pre19] - [Post19]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220628" Time="150108">CalculateField GW_DTWL_Data_2011_2020_89A FLU2019 "0" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220628" Time="150803">CalculateField GW_DTWL_Data_2011_2020_89A FLU2020 "[Pre20] - [Post20]" VB #</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.7\ArcToolbox\Toolboxes\Data Management Tools.tbx\CalculateField" Date="20220628" Time="150815">CalculateField GW_DTWL_Data_2011_2020_89A FLU2020 "0" VB #</Process></lineage><itemProps><itemLocation><linkage Sync="TRUE">file://\\INPC-KUPA\D$\63802313 DANIDA Udaipur Project\Danida_Udaipur_IWRA.mdb</linkage><protocol Sync="TRUE">Local Area Network</protocol></itemLocation><itemName Sync="TRUE">GW_DTWL_Data_2011_2020_WRT_Topography</itemName><imsContentType Sync="TRUE">002</imsContentType></itemProps><coordRef><type Sync="TRUE">Projected</type><geogcsn Sync="TRUE">GCS_WGS_1984</geogcsn><csUnits Sync="TRUE">Linear Unit: Meter (1.000000)</csUnits><projcsn Sync="TRUE">WGS_1984_UTM_Zone_43N</projcsn><peXml Sync="TRUE">&lt;ProjectedCoordinateSystem xsi:type='typens:ProjectedCoordinateSystem' xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance' xmlns:xs='http://www.w3.org/2001/XMLSchema' xmlns:typens='http://www.esri.com/schemas/ArcGIS/10.7'&gt;&lt;WKT&gt;PROJCS[&amp;quot;WGS_1984_UTM_Zone_43N&amp;quot;,GEOGCS[&amp;quot;GCS_WGS_1984&amp;quot;,DATUM[&amp;quot;D_WGS_1984&amp;quot;,SPHEROID[&amp;quot;WGS_1984&amp;quot;,6378137.0,298.257223563]],PRIMEM[&amp;quot;Greenwich&amp;quot;,0.0],UNIT[&amp;quot;Degree&amp;quot;,0.0174532925199433]],PROJECTION[&amp;quot;Transverse_Mercator&amp;quot;],PARAMETER[&amp;quot;False_Easting&amp;quot;,500000.0],PARAMETER[&amp;quot;False_Northing&amp;quot;,0.0],PARAMETER[&amp;quot;Central_Meridian&amp;quot;,75.0],PARAMETER[&amp;quot;Scale_Factor&amp;quot;,0.9996],PARAMETER[&amp;quot;Latitude_Of_Origin&amp;quot;,0.0],UNIT[&amp;quot;Meter&amp;quot;,1.0],AUTHORITY[&amp;quot;EPSG&amp;quot;,32643]]&lt;/WKT&gt;&lt;XOrigin&gt;-5120900&lt;/XOrigin&gt;&lt;YOrigin&gt;-9998100&lt;/YOrigin&gt;&lt;XYScale&gt;10000&lt;/XYScale&gt;&lt;ZOrigin&gt;-100000&lt;/ZOrigin&gt;&lt;ZScale&gt;10000&lt;/ZScale&gt;&lt;MOrigin&gt;-100000&lt;/MOrigin&gt;&lt;MScale&gt;10000&lt;/MScale&gt;&lt;XYTolerance&gt;0.001&lt;/XYTolerance&gt;&lt;ZTolerance&gt;0.001&lt;/ZTolerance&gt;&lt;MTolerance&gt;0.001&lt;/MTolerance&gt;&lt;HighPrecision&gt;true&lt;/HighPrecision&gt;&lt;WKID&gt;32643&lt;/WKID&gt;&lt;LatestWKID&gt;32643&lt;/LatestWKID&gt;&lt;/ProjectedCoordinateSystem&gt;</peXml></coordRef></DataProperties><SyncDate>20220302</SyncDate><SyncTime>17370600</SyncTime><ModDate>20220302</ModDate><ModTime>17370600</ModTime></Esri><dataIdInfo><envirDesc Sync="TRUE"> Version 6.2 (Build 9200) ; Esri ArcGIS 10.7.1.11595</envirDesc><dataLang><languageCode value="eng" Sync="TRUE"></languageCode><countryCode value="IND" Sync="TRUE"></countryCode></dataLang><idCitation><resTitle Sync="TRUE">GW_DTWL_Data_2011_2020_WRT_Topography</resTitle><presForm><PresFormCd value="005" Sync="TRUE"></PresFormCd></presForm></idCitation><spatRpType><SpatRepTypCd value="001" Sync="TRUE"></SpatRepTypCd></spatRpType></dataIdInfo><mdLang><languageCode value="eng" Sync="TRUE"></languageCode><countryCode value="IND" Sync="TRUE"></countryCode></mdLang><distInfo><distFormat><formatName Sync="TRUE">Personal GeoDatabase Feature Class</formatName></distFormat></distInfo><mdHrLv><ScopeCd value="005" Sync="TRUE"></ScopeCd></mdHrLv><mdHrLvName Sync="TRUE">dataset</mdHrLvName><refSysInfo><RefSystem><refSysID><identCode code="32643" Sync="TRUE"></identCode><idCodeSpace Sync="TRUE">EPSG</idCodeSpace><idVersion Sync="TRUE">2.1(3.0.1)</idVersion></refSysID></RefSystem></refSysInfo><spatRepInfo><VectSpatRep><geometObjs Name="GW_DTWL_Data_2011_2020_WRT_Topography"><geoObjTyp><GeoObjTypCd value="004" Sync="TRUE"></GeoObjTypCd></geoObjTyp><geoObjCnt Sync="TRUE">0</geoObjCnt></geometObjs><topLvl><TopoLevCd value="001" Sync="TRUE"></TopoLevCd></topLvl></VectSpatRep></spatRepInfo><spdoinfo><ptvctinf><esriterm Name="GW_DTWL_Data_2011_2020_WRT_Topography"><efeatyp Sync="TRUE">Simple</efeatyp><efeageom code="1" Sync="TRUE"></efeageom><esritopo Sync="TRUE">FALSE</esritopo><efeacnt Sync="TRUE">0</efeacnt><spindex Sync="TRUE">TRUE</spindex><linrefer Sync="TRUE">FALSE</linrefer></esriterm></ptvctinf></spdoinfo><eainfo><detailed Name="GW_DTWL_Data_2011_2020_WRT_Topography"><enttyp><enttypl Sync="TRUE">GW_DTWL_Data_2011_2020_WRT_Topography</enttypl><enttypt Sync="TRUE">Feature Class</enttypt><enttypc Sync="TRUE">0</enttypc></enttyp><attr><attrlabl Sync="TRUE">OBJECTID</attrlabl><attalias Sync="TRUE">OBJECTID</attalias><attrtype Sync="TRUE">OID</attrtype><attwidth Sync="TRUE">4</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale><attrdef Sync="TRUE">Internal feature number.</attrdef><attrdefs Sync="TRUE">Esri</attrdefs><attrdomv><udom Sync="TRUE">Sequential unique whole numbers that are automatically generated.</udom></attrdomv></attr><attr><attrlabl Sync="TRUE">Shape</attrlabl><attalias Sync="TRUE">Shape</attalias><attrtype Sync="TRUE">Geometry</attrtype><attwidth Sync="TRUE">0</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale><attrdef Sync="TRUE">Feature geometry.</attrdef><attrdefs Sync="TRUE">Esri</attrdefs><attrdomv><udom Sync="TRUE">Coordinates defining the features.</udom></attrdomv></attr><attr><attrlabl Sync="TRUE">WellType</attrlabl><attalias Sync="TRUE">WellType</attalias><attrtype Sync="TRUE">String</attrtype><attwidth Sync="TRUE">254</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">District</attrlabl><attalias Sync="TRUE">District</attalias><attrtype Sync="TRUE">String</attrtype><attwidth Sync="TRUE">254</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">Block</attrlabl><attalias Sync="TRUE">Block</attalias><attrtype Sync="TRUE">String</attrtype><attwidth Sync="TRUE">254</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">Location</attrlabl><attalias Sync="TRUE">Location</attalias><attrtype Sync="TRUE">String</attrtype><attwidth Sync="TRUE">254</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">Long_</attrlabl><attalias Sync="TRUE">Long_</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">Lat</attrlabl><attalias Sync="TRUE">Lat</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">Formation</attrlabl><attalias Sync="TRUE">Formation</attalias><attrtype Sync="TRUE">String</attrtype><attwidth Sync="TRUE">254</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">TotalDepth</attrlabl><attalias Sync="TRUE">TotalDepth</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">Pre11</attrlabl><attalias Sync="TRUE">Pre11</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">Post11</attrlabl><attalias Sync="TRUE">Post11</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">Pre12</attrlabl><attalias Sync="TRUE">Pre12</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">Post12</attrlabl><attalias Sync="TRUE">Post12</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">Pre13</attrlabl><attalias Sync="TRUE">Pre13</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">Post13</attrlabl><attalias Sync="TRUE">Post13</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">Pre14</attrlabl><attalias Sync="TRUE">Pre14</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">Post14</attrlabl><attalias Sync="TRUE">Post14</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">Pre15</attrlabl><attalias Sync="TRUE">Pre15</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">Post15</attrlabl><attalias Sync="TRUE">Post15</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">Pre16</attrlabl><attalias Sync="TRUE">Pre16</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">Post16</attrlabl><attalias Sync="TRUE">Post16</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">Pre17</attrlabl><attalias Sync="TRUE">Pre17</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">Post17</attrlabl><attalias Sync="TRUE">Post17</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">Pre18</attrlabl><attalias Sync="TRUE">Pre18</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">Post18</attrlabl><attalias Sync="TRUE">Post18</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">Pre19</attrlabl><attalias Sync="TRUE">Pre19</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">Post19</attrlabl><attalias Sync="TRUE">Post19</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">Pre20</attrlabl><attalias Sync="TRUE">Pre20</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">Post20</attrlabl><attalias Sync="TRUE">Post20</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">GroundElev</attrlabl><attalias Sync="TRUE">GroundElev</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">GE_Pre11</attrlabl><attalias Sync="TRUE">GE_Pre11</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">GE_Post11</attrlabl><attalias Sync="TRUE">GE_Post11</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">GE_Pre12</attrlabl><attalias Sync="TRUE">GE_Pre12</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">GE_Post12</attrlabl><attalias Sync="TRUE">GE_Post12</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">GE_Pre13</attrlabl><attalias Sync="TRUE">GE_Pre13</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">GE_Post13</attrlabl><attalias Sync="TRUE">GE_Post13</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">GE_Pre14</attrlabl><attalias Sync="TRUE">GE_Pre14</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">GE_Post14</attrlabl><attalias Sync="TRUE">GE_Post14</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">GE_Pre15</attrlabl><attalias Sync="TRUE">GE_Pre15</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">GE_Post15</attrlabl><attalias Sync="TRUE">GE_Post15</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">GE_Pre16</attrlabl><attalias Sync="TRUE">GE_Pre16</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">GE_Post16</attrlabl><attalias Sync="TRUE">GE_Post16</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">GE_Pre17</attrlabl><attalias Sync="TRUE">GE_Pre17</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">GE_Post17</attrlabl><attalias Sync="TRUE">GE_Post17</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">GE_Pre18</attrlabl><attalias Sync="TRUE">GE_Pre18</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">GE_Post18</attrlabl><attalias Sync="TRUE">GE_Post18</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">GE_Pre19</attrlabl><attalias Sync="TRUE">GE_Pre19</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">GE_Post19</attrlabl><attalias Sync="TRUE">GE_Post19</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">GE_Pre20</attrlabl><attalias Sync="TRUE">GE_Pre20</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">GE_Post20</attrlabl><attalias Sync="TRUE">GE_Post20</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">8</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr></detailed></eainfo><mdDateSt Sync="TRUE">20220302</mdDateSt></metadata>
