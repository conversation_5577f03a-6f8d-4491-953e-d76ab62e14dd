const map = new ol.Map({
layers: [
	new ol.layer.Tile({
	  source: new ol.source.OSM(),
	}),
  ],
  target: 'map',
  view: new ol.View({
	center: [0, 0],
	zoom: 2,
  }),
});

var vector = new ol.layer.Vector({
	title: 'added Layer',
	source: new ol.source.Vector({
		//url: './geojsons/DistrictWiseCatchmentArea.json',
		url: './geojsons/Districwisedata.json',
		format: new ol.format.GeoJSON({
			dataProjection: 'EPSG:4326',
			featureProjection: map.getView().getProjection()
		}),
	})
});

map.addLayer(vector);

vector.on("change", function(e) {
    map.getView().fit(vector.getSource().getExtent(), map.getSize());
});



document.getElementById('zoom-out').onclick = function () {
  const view = map.getView();
  const zoom = view.getZoom();
  view.setZoom(zoom - 1);
};

document.getElementById('zoom-in').onclick = function () {
  const view = map.getView();
  const zoom = view.getZoom();
  view.setZoom(zoom + 1);
};