html {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

body {
  position: relative;
  font-family: '<PERSON>o', sans-serif;
  font-size: 14px;
  width: 100%;
  height: 100%;
  background-color: #f2f5f7;
}

.head {
  width: 100%;
  height: 60px;
  background-color: #f2f5f7;
  border-bottom: 4px solid #dbe4e9;
  position: fixed;
  top: 0;
  left: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  z-index: 1;
}

.head .logo {
  height: 36px;
}

@media (min-width: 576px) {
  .head .logo {
    height: 42px;
  }
}

.head h1 {
  color: #333333;
  font-weight: 700;
  font-size: 12px;
}

@media (min-width: 576px) {
  .head h1 {
    font-size: 14px;
  }
}

@media (min-width: 768px) {
  .head {
    position: relative;
    top: auto;
    left: auto;
  }
}

.banner {
  width: 100%;
  height: 450px;
  background-image: url(../img/banner.jpg);
  background-repeat: no-repeat;
  background-size: cover;
  margin-top: 60px;
  z-index: 0;
}

.banner .caption {
  background-color: rgba(255, 255, 255, 0.75);
  font-weight: 400;
  font-size: 20px;
  width: 100%;
  text-align: center;
  line-height: 36px;
  margin-left: 10px;
  margin-right: 10px;
  border-radius: 4px;
}

@media (min-width: 768px) {
  .banner .caption {
    width: 60%;
    font-size: 24px;
    text-align: left;
    margin-left: 50px;
    margin-right: 50px;
  }
}

@media (min-width: 768px) {
  .banner {
    margin-top: 0;
  }
}

.body {
  position: relative;
  z-index: 0;
}

.body .desc {
  color: #333333;
}

.body h3 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 100%;
  background-color: white;
  font-size: 14px;
  font-weight: 700;
  height: 60px;
  padding-left: 20px;
  padding-right: 60px;
  position: relative;
  border-bottom: 1px solid #dbe4e9;
  margin-top: 5px;
  -webkit-transition: 0.2s;
  transition: 0.2s;
  cursor: pointer;
}

.body h3 span {
  position: absolute;
  right: 20px;
  font-size: 20px;
  font-weight: 700;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

.body h3.active span {
  -webkit-transform: rotate(-180deg);
          transform: rotate(-180deg);
}

.body h3.active, .body h3:hover {
  background-color: #DBE4E9;
}

.body .content {
  background-color: white;
  border-top: none;
  margin-top: -8px;
  max-height: 0;
  overflow: hidden;
  -webkit-transition: max-height 0.3s ease-in-out;
  transition: max-height 0.3s ease-in-out;
}

.body .content p {
  text-align: justify;
}

.body .content li {
  margin-bottom: 5px;
}

.body .content .alpha {
  margin-top: 2px;
  list-style-type: lower-alpha;
}

.body h4 {
  color: #0b4566;
  font-weight: 700;
  font-size: 14px;
}

.body .mofa {
  color: #333333;
}

.body .mofa img {
  max-width: 300px;
}

.body .mofa a {
  color: #0b4566;
  font-weight: 700;
}

.body .mofa strong {
  color: #000000;
}

.body .partnerList {
  padding: 0;
}

.body .partnerList li {
  list-style-type: none;
  margin-bottom: 20px;
}

.body .partnerList li a {
  text-decoration: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  color: #333333;
}

.body .partnerList li a img {
  margin-left: 10px;
  margin-right: 20px;
}

.body .partnerList li a p {
  margin-bottom: 0;
  padding-right: 20px;
}

.body .partnerList li a:hover {
  text-decoration: underline;
}

.footer {
  width: 100%;
  background-color: white;
}

.footer h4 {
  color: #0b4566;
  font-weight: 700;
  font-size: 14px;
}

.footer .mofa img {
  max-width: 250px;
}

.footer .partnerList {
  padding: 0;
  max-width: 300px;
  list-style-type: none;
}

.footer .partnerList a {
  text-decoration: none;
  color: #557A8F;
}

.footer .partnerList a:hover {
  text-decoration: underline;
  color: #86A2B3;
}

.footer .contactPerson {
  color: #557A8F;
}

.footer .contactPerson a {
  color: #00A4EC;
}

.copyright {
  text-align: center;
  font-size: 12px;
  background-color: #09334B;
  color: #86A2B3;
}
/*# sourceMappingURL=style.css.map */