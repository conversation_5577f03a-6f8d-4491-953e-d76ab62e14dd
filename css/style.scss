$screen-xs-min: 0;
$screen-sm-min: 576px;
$screen-md-min: 768px;
$screen-lg-min: 992px;
$screen-xl-min: 1200px;
$screen-hd-min: 1560px;
$screen-fhd-min: 1900px;

@mixin xs {
    @media (min-width: #{$screen-xs-min}) { @content; }
}
@mixin sm {
    @media (min-width: #{$screen-sm-min}) { @content; }
}
@mixin md {
    @media (min-width: #{$screen-md-min}) { @content; }
}
@mixin lg {
    @media (min-width: #{$screen-lg-min}) { @content; }
}
@mixin xl {
    @media (min-width: #{$screen-xl-min}) { @content; }
}
@mixin hd {
    @media (min-width: #{$screen-hd-min}) { @content; }
}
@mixin fhd {
    @media (min-width: #{$screen-fhd-min}) { @content; }
}

html{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
body{
    position: relative;
    font-family: 'Roboto', sans-serif;
    font-size: 14px;
    width: 100%;
    height: 100%;
    background-color: #f2f5f7;
}

.head{
    width: 100%;
    height: 60px;
    background-color: #f2f5f7;
    border-bottom: 4px solid #dbe4e9;
    position: fixed;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    z-index: 1;
    .logo{
        height: 36px;
        @include sm{
            height: 42px;
        }
    }
    h1{
        color: #333333;
        font-weight: 700;
        font-size: 12px;
        @include sm{
            font-size: 14px;
        }
    }
    @include md{
        position: relative;
        top: auto;
        left: auto;
    }
}
.banner{
    width: 100%;
    height: 450px;
    background-image: url(../img/banner.jpg);
    background-repeat:no-repeat;
    background-size: cover;
    margin-top: 60px;
    z-index: 0;
    .caption{
        background-color: rgba(255,255,255,0.75);
        font-weight: 400;
        font-size: 20px;
        width: 100%;
        text-align: center;
        line-height: 36px;
        margin-left: 10px;
        margin-right: 10px;
        border-radius: 4px;
        @include md{
            width: 60%;
            font-size: 24px;
            text-align: left;
            margin-left: 50px;
            margin-right: 50px;
        }
    }
    @include md{
        margin-top: 0;
    }
}
.body{
    position: relative;
    z-index: 0;
    .desc{
        color: #333333;
    }
    h3{
        display: flex;
        align-items: center;
        width: 100%;
        background-color: white;
        font-size: 14px;
        font-weight: 700;
        height: 60px;
        padding-left: 20px;
        padding-right: 60px;
        position: relative;
        border-bottom: 1px solid #dbe4e9;
        margin-top: 5px;
        transition: 0.2s;
        cursor: pointer;
        span{
            position: absolute;
            right: 20px;
            font-size: 20px;
            font-weight: 700;
            transition: 0.4s;
        }
    }
    h3.active {
        span{
            transform: rotate(-180deg);
        }
    }
    h3.active, h3:hover {
        background-color: #DBE4E9;
    }
    .content{
        background-color: white;
        border-top: none;
        margin-top: -8px;
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease-in-out;
        p{
            text-align: justify;
        }
        li{
            margin-bottom: 5px;
        }
        .alpha{
            margin-top: 2px;
            list-style-type: lower-alpha;
        }
    }
    h4{
        color: #0b4566;
        font-weight: 700;
        font-size: 14px;
    }
    .mofa{
        img{
            max-width: 300px;
        }
        color: #333333;
        a{
            color: #0b4566;
            font-weight: 700;
        }
        strong{
            color: #000000;
        }
    }
    .partnerList{
        padding: 0;
        li{
            list-style-type: none;
            margin-bottom: 20px;
            a{
                text-decoration: none;
                display: flex;
                align-items: center;
                color: #333333;
                img{
                    margin-left: 10px;
                    margin-right: 20px;
                }
                p{
                    margin-bottom: 0;
                    padding-right: 20px;
                }
            }
            a:hover{
                text-decoration: underline;
            }
        }
    }
}
.footer{
    width: 100%;
    background-color: white;
    h4{
        color: #0b4566;
        font-weight: 700;
        font-size: 14px;
    }
    .mofa{
        img{
            max-width: 250px;
        }
        
    }
    .partnerList{
        padding: 0;
        max-width: 300px;
        list-style-type: none;
        a{
            text-decoration: none;
            color: #557A8F;
        }
        a:hover{
            text-decoration: underline;
            color: #86A2B3;
        }
    }
    .contactPerson{
        color: #557A8F;
        a{
            color: #00A4EC;
        }
    }
}
.copyright{
    text-align: center;
    font-size: 12px;
    background-color: #09334B;
    color: #86A2B3;
}