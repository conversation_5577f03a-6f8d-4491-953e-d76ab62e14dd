@font-face {
  font-family: 'icomoon';
  src:  url('fonts/icomoon.eot?dwzu3z');
  src:  url('fonts/icomoon.eot?dwzu3z#iefix') format('embedded-opentype'),
    url('fonts/icomoon.ttf?dwzu3z') format('truetype'),
    url('fonts/icomoon.woff?dwzu3z') format('woff'),
    url('fonts/icomoon.svg?dwzu3z#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-chevron-down:before {
  content: "\e930";
}
